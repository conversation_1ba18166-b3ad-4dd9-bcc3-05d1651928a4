# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 日志文件
logs/*.log
logs/*.log.*
start_all.log

# 测试文件
.pytest_cache/
.coverage
htmlcov/

# Git文件
.git/
.gitignore

# Docker文件
Dockerfile
.dockerignore

# 文档文件
docs/
*.md

# 保留配置文件
!config.yml

# 临时文件
*.tmp
*.temp

# 操作系统文件
.DS_Store
Thumbs.db

# 数据文件（如果有的话）
*.sqlite3
*.db

# 静态文件收集目录
static/

# 媒体文件
media/

# 环境变量文件
.env
.env.local
.env.production

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 批处理文件
*.bat
*.ps1
