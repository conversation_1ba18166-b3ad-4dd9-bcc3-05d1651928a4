# 用户行为分析系统 - 部署指南

本文档介绍如何从代码仓库拉取代码并使用Docker部署系统。

## 🚀 快速部署

### 1. 拉取代码

```bash
# 克隆仓库
git clone <repository-url>
cd userbehavior

# 或者更新现有代码
git pull origin main
```

### 2. 构建并运行

```bash
# 方法一：使用部署脚本（推荐）
python docker_deploy.py build-run

# 方法二：手动执行
docker build -t userbehavior-app:latest .
mkdir -p conf logs data
cp config.example.yml conf/config.yml
docker run -d \
  --name userbehavior-app \
  -p 8000:8000 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v $(pwd)/conf:/app/conf \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:latest
```

### 3. 验证部署

```bash
# 检查容器状态
docker ps

# 检查服务健康
curl http://localhost:8000/api/analytics/health/

# 查看日志
docker logs userbehavior-app
```

## 📁 目录结构

部署后会创建以下目录结构：

```
./
├── conf/
│   └── config.yml          # 配置文件（从外部映射）
├── logs/                   # 日志目录（从容器映射）
│   ├── django-web.log      # Django Web服务日志
│   ├── django-error.log    # Django错误日志
│   ├── docker_start.log    # Docker启动日志
│   └── scheduler/          # 调度器日志目录
│       └── scheduler-service.log
└── data/                   # 数据目录（从容器映射）
```

## ⚙️ 配置管理

### 配置文件说明

系统使用 `conf/config.yml` 进行配置，支持：

- **数据库配置**: MySQL连接信息
- **Elasticsearch配置**: ES集群连接信息
- **Redis配置**: 缓存服务配置
- **调度器配置**: 定时任务执行时间
- **应用配置**: Django基础设置

### 修改配置

```bash
# 编辑配置文件
vim conf/config.yml

# 重启容器使配置生效
docker restart userbehavior-app
```

### 配置示例

```yaml
# conf/config.yml
app:
  debug: false
  secret_key: "your-production-secret-key"
  allowed_hosts: ["yourdomain.com"]

database:
  host: "your-mysql-host"
  port: 3306
  name: "user_behavior_db"
  user: "your-db-user"
  password: "your-db-password"

elasticsearch:
  hosts: ["your-es-host:9200"]
  username: "elastic"
  password: "your-es-password"

redis:
  host: "your-redis-host"
  port: 6379
  db: 1
```

## 🌍 多环境部署

### 开发环境

```bash
# 使用开发配置
python docker_deploy.py build-run \
  --name userbehavior-dev \
  --port 8000 \
  --conf-dir ./conf/dev \
  --logs-dir ./logs/dev

# 配置文件: conf/dev/config.yml
```

### 生产环境

```bash
# 使用生产配置
python docker_deploy.py build-run \
  --name userbehavior-prod \
  --port 9997 \
  --conf-dir ./conf/prod \
  --logs-dir ./logs/prod

# 配置文件: conf/prod/config.yml
```

### 多端口部署

```bash
# 端口9997
python docker_deploy.py build-run \
  --name userbehavior-9997 \
  --port 9997 \
  --conf-dir ./conf/9997 \
  --logs-dir ./logs/9997

# 端口9998
python docker_deploy.py build-run \
  --name userbehavior-9998 \
  --port 9998 \
  --conf-dir ./conf/9998 \
  --logs-dir ./logs/9998
```

## 🔧 运维管理

### 查看状态

```bash
# 查看容器状态
python docker_deploy.py status

# 查看容器日志
python docker_deploy.py logs

# 查看特定容器日志
python docker_deploy.py logs --name userbehavior-prod
```

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
python docker_deploy.py build-run

# 或者分步执行
python docker_deploy.py build
docker stop userbehavior-app
docker rm userbehavior-app
python docker_deploy.py run
```

### 备份和恢复

```bash
# 备份配置和日志
tar -czf backup-$(date +%Y%m%d).tar.gz conf/ logs/ data/

# 恢复配置
tar -xzf backup-20250101.tar.gz
```

## 🐛 故障排除

### 容器启动失败

```bash
# 查看容器日志
docker logs userbehavior-app

# 查看详细错误
docker logs --details userbehavior-app

# 进入容器调试
docker exec -it userbehavior-app /bin/bash
```

### 配置文件问题

```bash
# 检查配置文件语法
python -c "import yaml; yaml.safe_load(open('conf/config.yml'))"

# 使用默认配置
cp config.example.yml conf/config.yml
docker restart userbehavior-app
```

### 端口冲突

```bash
# 查看端口占用
netstat -tlnp | grep :8000

# 使用其他端口
python docker_deploy.py run --port 8001
```

### 权限问题

```bash
# 检查目录权限
ls -la conf/ logs/ data/

# 修复权限
chmod -R 755 conf/ logs/ data/
```

## 📊 监控和日志

### 日志查看

```bash
# 实时查看Django日志
tail -f logs/django-web.log

# 实时查看调度器日志
tail -f logs/scheduler/scheduler-service.log

# 查看错误日志
tail -f logs/django-error.log
```

### 性能监控

```bash
# 查看容器资源使用
docker stats userbehavior-app

# 查看容器详细信息
docker inspect userbehavior-app
```

## 🔒 安全配置

### 生产环境建议

1. **修改默认密钥**
   ```yaml
   app:
     secret_key: "strong-random-secret-key"
   ```

2. **限制访问主机**
   ```yaml
   app:
     allowed_hosts: ["yourdomain.com", "www.yourdomain.com"]
   ```

3. **使用HTTPS**
   ```yaml
   app:
     use_https: true
   ```

4. **设置防火墙**
   ```bash
   # 只允许特定端口
   ufw allow 9997/tcp
   ufw enable
   ```

## 📋 检查清单

部署前检查：

- [ ] 代码已拉取到最新版本
- [ ] Docker已安装并运行
- [ ] 端口未被占用
- [ ] 配置文件已准备
- [ ] 外部依赖服务可访问（MySQL、ES、Redis）

部署后验证：

- [ ] 容器正常运行
- [ ] 健康检查通过
- [ ] 日志无错误
- [ ] API接口可访问
- [ ] 调度器服务正常

这样就完成了从代码仓库到Docker部署的完整流程！
