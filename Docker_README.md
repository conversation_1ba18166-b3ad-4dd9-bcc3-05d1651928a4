# 用户行为分析系统 - Docker部署

本项目提供了完整的Docker容器化解决方案，可以一键部署Django Web服务和调度器服务。

## 🚀 快速开始

### 方法一：使用构建脚本（推荐）

```bash
# 构建并运行容器
python build_docker.py build-run

# 查看服务状态
python build_docker.py status

# 查看日志
python build_docker.py logs
```

### 方法二：使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方法三：手动Docker命令

```bash
# 构建镜像
docker build -t userbehavior-app:latest .

# 运行容器
docker run -d \
  --name userbehavior-app \
  -p 8000:8000 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:latest
```

## 📋 服务说明

容器启动后会自动运行两个服务：

1. **Django Web服务** (端口8000)
   - 提供REST API接口
   - 用户认证和权限管理
   - 数据分析和报表功能

2. **调度器服务** (后台运行)
   - 每天凌晨1:00自动执行数据处理任务
   - 处理所有系统类型的数据 (api, oss, boss)
   - 独立的日志记录

## 🔧 构建脚本使用

`build_docker.py` 脚本提供了完整的Docker管理功能：

```bash
# 构建镜像
python build_docker.py build

# 运行容器
python build_docker.py run

# 构建并运行
python build_docker.py build-run

# 使用Docker Compose
python build_docker.py compose-up
python build_docker.py compose-down
python build_docker.py compose-logs

# 查看状态和日志
python build_docker.py status
python build_docker.py logs

# 清理资源
python build_docker.py cleanup
```

## 🌐 访问地址

服务启动后可通过以下地址访问：

- **API基础地址**: http://localhost:8000/api/
- **健康检查**: http://localhost:8000/api/analytics/health/
- **用户登录**: http://localhost:8000/api/analytics/users/auth/login/
- **操作日志**: http://localhost:8000/api/analytics/operation-logs/
- **访问统计**: http://localhost:8000/api/analytics/access-stats/

## 📊 默认用户

系统提供默认管理员账户：

- **用户名**: admin
- **密码**: tgt51848

## 📝 日志管理

### 日志文件位置

- Django Web服务: `logs/django-web.log`
- Django错误日志: `logs/django-error.log`
- 调度器服务: `logs/scheduler/scheduler-service.log`
- Docker启动日志: `logs/docker_start.log`

### 查看日志命令

```bash
# 查看容器日志
docker logs -f userbehavior-app

# 查看特定服务日志
tail -f logs/django-web.log
tail -f logs/scheduler/scheduler-service.log

# 使用构建脚本查看日志
python build_docker.py logs
```

## 🔍 健康检查

容器配置了自动健康检查：

```bash
# 检查容器健康状态
docker ps

# 手动健康检查
curl http://localhost:8000/api/analytics/health/
```

## ⚙️ 配置说明

### 环境变量

- `PYTHONUNBUFFERED=1`: 禁用Python输出缓冲
- `TZ=Asia/Shanghai`: 设置时区为北京时间

### 数据卷挂载

- `./logs:/app/logs`: 日志文件持久化
- `./data:/app/data`: 数据文件持久化

### 端口映射

- `8000:8000`: Django Web服务端口

## 🛠️ 故障排除

### 1. 容器启动失败

```bash
# 查看详细日志
docker logs userbehavior-app

# 进入容器调试
docker exec -it userbehavior-app /bin/bash
```

### 2. 服务无法访问

```bash
# 检查端口映射
docker port userbehavior-app

# 检查防火墙设置
curl http://localhost:8000/api/analytics/health/
```

### 3. 数据库连接问题

确保外部MySQL服务可访问，检查 `src/config/settings.py` 中的数据库配置。

### 4. Elasticsearch连接问题

确保外部ES服务可访问，检查 `src/config/settings.py` 中的ES配置。

## 🔄 更新部署

```bash
# 停止现有服务
python build_docker.py compose-down

# 重新构建并启动
python build_docker.py compose-build
python build_docker.py compose-up
```

## 🧹 清理资源

```bash
# 使用构建脚本清理
python build_docker.py cleanup

# 手动清理
docker stop userbehavior-app
docker rm userbehavior-app
docker rmi userbehavior-app:latest
docker system prune -f
```

## 📚 相关文档

- [完整Docker部署指南](docs/docker_deployment_guide.md)
- [API接口文档](docs/api/)
- [系统部署指南](docs/deployment_guide.md)

## 🆘 技术支持

如果遇到问题，请检查：

1. Docker和Docker Compose版本是否符合要求
2. 端口8000是否被其他服务占用
3. 外部依赖服务（MySQL、Redis、Elasticsearch）是否可访问
4. 日志文件中的错误信息

更多详细信息请参考 [Docker部署指南](docs/docker_deployment_guide.md)。
