# 使用Python 3.8官方镜像作为基础镜像
FROM python:3.8-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # MySQL客户端依赖
    default-libmysqlclient-dev \
    build-essential \
    pkg-config \
    # 时区设置
    tzdata \
    # 网络工具（可选，用于调试）
    curl \
    wget \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs/scheduler logs/task data static

# 设置文件权限
RUN chmod +x run_server.py simple_scheduler.py start_all.py

# 创建启动脚本
RUN cat > /app/docker_start.py << 'EOF'
#!/usr/bin/env python
# Docker容器启动脚本

import os
import sys
import time
import subprocess
import signal
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/app/logs/docker_start.log')
    ]
)

logger = logging.getLogger(__name__)

# 进程列表
processes = []

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在关闭服务...")
    stop_services()
    sys.exit(0)

def start_services():
    """启动所有服务"""
    logger.info("Docker容器启动，开始启动服务...")
    
    # 启动Django服务器
    logger.info("启动Django服务器...")
    django_process = subprocess.Popen([
        sys.executable, '/app/run_server.py'
    ], cwd='/app')
    processes.append(('Django服务器', django_process))
    logger.info(f"Django服务器已启动，PID: {django_process.pid}")
    
    # 等待Django服务器启动完成
    logger.info("等待Django服务器启动完成...")
    time.sleep(10)
    
    # 启动简单调度器
    logger.info("启动简单调度器...")
    scheduler_process = subprocess.Popen([
        sys.executable, '/app/simple_scheduler.py'
    ], cwd='/app')
    processes.append(('简单调度器', scheduler_process))
    logger.info(f"简单调度器已启动，PID: {scheduler_process.pid}")
    
    logger.info("所有服务已启动")
    
    # 监控进程状态
    try:
        while True:
            all_running = True
            for name, process in processes:
                if process.poll() is not None:
                    logger.error(f"{name}已停止，退出代码: {process.returncode}")
                    all_running = False
            
            if not all_running:
                logger.error("检测到服务停止，正在关闭所有服务...")
                stop_services()
                break
            
            time.sleep(10)
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
        stop_services()

def stop_services():
    """停止所有服务"""
    logger.info("正在停止所有服务...")
    
    for name, process in processes:
        if process.poll() is None:  # 如果进程仍在运行
            logger.info(f"正在停止{name}...")
            try:
                # 尝试优雅地终止进程
                process.terminate()
                
                # 等待进程终止
                process.wait(timeout=10)
                logger.info(f"{name}已停止")
            except subprocess.TimeoutExpired:
                # 如果进程没有及时终止，强制终止
                logger.warning(f"{name}没有响应，强制终止...")
                process.kill()
                logger.info(f"{name}已强制终止")
    
    logger.info("所有服务已停止")

if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    start_services()
EOF

# 设置启动脚本权限
RUN chmod +x /app/docker_start.py

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/analytics/health/ || exit 1

# 启动命令
CMD ["python", "/app/docker_start.py"]
