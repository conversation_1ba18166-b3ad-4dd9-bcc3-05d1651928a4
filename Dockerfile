# 使用Python 3.8官方镜像作为基础镜像
FROM python:3.8-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # MySQL客户端依赖
    default-libmysqlclient-dev \
    build-essential \
    pkg-config \
    # 时区设置
    tzdata \
    # 网络工具（可选，用于调试）
    curl \
    wget \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建配置文件目录
RUN mkdir -p /app/conf

# 创建必要的目录
RUN mkdir -p logs/scheduler logs/task data static

# 设置文件权限
RUN chmod +x run_server.py run_server_with_config.py simple_scheduler.py start_all.py

# 复制启动脚本
COPY docker_start.py /app/docker_start.py

# 设置启动脚本权限
RUN chmod +x /app/docker_start.py

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/analytics/health/ || exit 1

# 启动命令
CMD ["python", "/app/docker_start.py"]
