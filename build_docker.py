#!/usr/bin/env python
# Docker构建和部署脚本

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime

def run_command(cmd, description=""):
    """执行命令并处理错误"""
    print(f"\n{'='*50}")
    if description:
        print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print("输出:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 命令执行失败")
        print(f"返回码: {e.returncode}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def build_image(tag="userbehavior-app:latest"):
    """构建Docker镜像"""
    print(f"开始构建Docker镜像: {tag}")
    
    # 检查Dockerfile是否存在
    if not os.path.exists("Dockerfile"):
        print("错误: 未找到Dockerfile文件")
        return False
    
    # 构建镜像
    cmd = f"docker build -t {tag} ."
    return run_command(cmd, "构建Docker镜像")

def run_container(tag="userbehavior-app:latest", container_name="userbehavior-app", port=8000):
    """运行Docker容器"""
    print(f"开始运行Docker容器: {container_name}")
    
    # 停止并删除现有容器（如果存在）
    print("检查并清理现有容器...")
    run_command(f"docker stop {container_name}", "停止现有容器")
    run_command(f"docker rm {container_name}", "删除现有容器")
    
    # 创建日志和数据目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    
    # 运行新容器
    cmd = f"""docker run -d \
        --name {container_name} \
        -p {port}:8000 \
        -v {os.getcwd()}/logs:/app/logs \
        -v {os.getcwd()}/data:/app/data \
        {tag}"""
    
    return run_command(cmd, "运行Docker容器")

def use_compose(action="up"):
    """使用Docker Compose管理服务"""
    if not os.path.exists("docker-compose.yml"):
        print("错误: 未找到docker-compose.yml文件")
        return False
    
    if action == "up":
        cmd = "docker-compose up -d"
        description = "启动Docker Compose服务"
    elif action == "down":
        cmd = "docker-compose down"
        description = "停止Docker Compose服务"
    elif action == "logs":
        cmd = "docker-compose logs -f"
        description = "查看Docker Compose日志"
    elif action == "build":
        cmd = "docker-compose build"
        description = "构建Docker Compose服务"
    else:
        print(f"错误: 不支持的操作 {action}")
        return False
    
    return run_command(cmd, description)

def show_status():
    """显示容器状态"""
    print("\n" + "="*50)
    print("Docker容器状态")
    print("="*50)
    
    run_command("docker ps", "查看运行中的容器")
    
    print("\n" + "="*50)
    print("Docker镜像列表")
    print("="*50)
    
    run_command("docker images | grep userbehavior", "查看相关镜像")

def show_logs(container_name="userbehavior-app"):
    """显示容器日志"""
    print(f"\n显示容器 {container_name} 的日志:")
    print("="*50)
    
    # 显示最近的日志
    run_command(f"docker logs --tail 50 {container_name}", "查看容器日志")

def cleanup():
    """清理Docker资源"""
    print("\n开始清理Docker资源...")
    
    # 停止并删除容器
    run_command("docker stop userbehavior-app", "停止容器")
    run_command("docker rm userbehavior-app", "删除容器")
    
    # 删除镜像
    run_command("docker rmi userbehavior-app:latest", "删除镜像")
    
    # 清理未使用的资源
    run_command("docker system prune -f", "清理未使用的资源")

def main():
    parser = argparse.ArgumentParser(description="Docker构建和部署脚本")
    parser.add_argument("action", choices=[
        "build", "run", "build-run", "compose-up", "compose-down", 
        "compose-logs", "compose-build", "status", "logs", "cleanup"
    ], help="要执行的操作")
    parser.add_argument("--tag", default="userbehavior-app:latest", help="Docker镜像标签")
    parser.add_argument("--name", default="userbehavior-app", help="容器名称")
    parser.add_argument("--port", type=int, default=8000, help="端口映射")
    
    args = parser.parse_args()
    
    print(f"Docker部署脚本 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"执行操作: {args.action}")
    
    success = True
    
    if args.action == "build":
        success = build_image(args.tag)
    elif args.action == "run":
        success = run_container(args.tag, args.name, args.port)
    elif args.action == "build-run":
        success = build_image(args.tag)
        if success:
            success = run_container(args.tag, args.name, args.port)
    elif args.action == "compose-up":
        success = use_compose("up")
    elif args.action == "compose-down":
        success = use_compose("down")
    elif args.action == "compose-logs":
        success = use_compose("logs")
    elif args.action == "compose-build":
        success = use_compose("build")
    elif args.action == "status":
        show_status()
    elif args.action == "logs":
        show_logs(args.name)
    elif args.action == "cleanup":
        cleanup()
    
    if success:
        print(f"\n✅ 操作 '{args.action}' 执行成功!")
        
        if args.action in ["run", "build-run", "compose-up"]:
            print(f"\n🌐 服务访问地址: http://localhost:{args.port}")
            print("📋 API文档: http://localhost:8000/api/")
            print("🔍 健康检查: http://localhost:8000/api/analytics/health/")
            
            print("\n📝 查看日志命令:")
            print(f"  docker logs -f {args.name}")
            print("  python build_docker.py logs")
            
            print("\n🛑 停止服务命令:")
            if args.action.startswith("compose"):
                print("  python build_docker.py compose-down")
            else:
                print(f"  docker stop {args.name}")
    else:
        print(f"\n❌ 操作 '{args.action}' 执行失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
