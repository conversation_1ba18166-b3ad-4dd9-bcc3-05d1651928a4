#!/usr/bin/env python
# Docker环境检查脚本

import os
import sys
import subprocess
import platform

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """检查Docker环境"""
    print("=" * 60)
    print("Docker环境检查")
    print("=" * 60)
    
    # 检查操作系统
    os_name = platform.system()
    print(f"操作系统: {os_name} {platform.release()}")
    
    # 检查Docker命令是否可用
    print("\n1. 检查Docker命令...")
    success, stdout, stderr = run_command("docker --version")
    if success:
        print(f"✅ Docker已安装: {stdout}")
    else:
        print(f"❌ Docker命令不可用")
        print(f"   错误信息: {stderr}")
        return False
    
    # 检查Docker是否运行
    print("\n2. 检查Docker服务状态...")
    success, stdout, stderr = run_command("docker info")
    if success:
        print("✅ Docker服务正在运行")
    else:
        print("❌ Docker服务未运行")
        print(f"   错误信息: {stderr}")
        if os_name == "Windows":
            print("   请启动Docker Desktop")
        else:
            print("   请启动Docker服务: sudo systemctl start docker")
        return False
    
    # 检查Docker版本
    print("\n3. 检查Docker版本...")
    success, stdout, stderr = run_command("docker version")
    if success:
        lines = stdout.split('\n')
        for line in lines:
            if 'Version:' in line:
                print(f"   {line.strip()}")
    
    # 测试Docker功能
    print("\n4. 测试Docker功能...")
    success, stdout, stderr = run_command("docker run --rm hello-world")
    if success:
        print("✅ Docker功能正常")
    else:
        print("❌ Docker功能测试失败")
        print(f"   错误信息: {stderr}")
        return False
    
    print("\n✅ Docker环境检查通过！")
    return True

def check_project_files():
    """检查项目文件"""
    print("\n" + "=" * 60)
    print("项目文件检查")
    print("=" * 60)
    
    required_files = [
        "Dockerfile",
        "requirements.txt",
        "docker_start.py",
        "run_server.py",
        "simple_scheduler.py",
        "config.example.yml"
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 文件不存在")
            all_exist = False
    
    return all_exist

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案")
    print("=" * 60)
    
    os_name = platform.system()
    
    if os_name == "Windows":
        print("Windows系统解决方案:")
        print("1. 安装Docker Desktop for Windows")
        print("   下载地址: https://www.docker.com/products/docker-desktop")
        print("2. 启动Docker Desktop")
        print("3. 确保Docker Desktop在系统托盘中运行")
        print("4. 重新打开命令行窗口")
        print("5. 运行: deploy.bat")
    else:
        print("Linux/macOS系统解决方案:")
        print("1. 安装Docker:")
        print("   Ubuntu: sudo apt-get install docker.io")
        print("   CentOS: sudo yum install docker")
        print("   macOS: 安装Docker Desktop")
        print("2. 启动Docker服务:")
        print("   sudo systemctl start docker")
        print("3. 运行: ./deploy.sh")
    
    print("\n手动部署命令:")
    print("docker build -t userbehavior-app:latest .")
    print("mkdir -p conf logs data")
    print("cp config.example.yml conf/config.yml")
    if os_name == "Windows":
        print("docker run -d --name userbehavior-app -p 8000:8000 -e CONFIG_FILE_PATH=/app/conf/config.yml -v %cd%\\conf:/app/conf -v %cd%\\logs:/app/logs -v %cd%\\data:/app/data userbehavior-app:latest")
    else:
        print("docker run -d --name userbehavior-app -p 8000:8000 -e CONFIG_FILE_PATH=/app/conf/config.yml -v $(pwd)/conf:/app/conf -v $(pwd)/logs:/app/logs -v $(pwd)/data:/app/data userbehavior-app:latest")

def main():
    """主函数"""
    print("用户行为分析系统 - 环境检查工具")
    
    # 检查Docker环境
    docker_ok = check_docker()
    
    # 检查项目文件
    files_ok = check_project_files()
    
    if docker_ok and files_ok:
        print("\n🎉 环境检查通过，可以开始部署！")
        print("\n推荐部署命令:")
        if platform.system() == "Windows":
            print("  deploy.bat")
        else:
            print("  ./deploy.sh")
        print("  python docker_deploy.py build-run")
    else:
        print("\n❌ 环境检查失败，请解决以下问题:")
        if not docker_ok:
            print("  - Docker环境问题")
        if not files_ok:
            print("  - 项目文件缺失")
        
        provide_solutions()

if __name__ == "__main__":
    main()
