# 用户行为分析系统配置文件示例
# 复制此文件为 config.yml 并根据实际环境修改配置

# 应用基础配置
app:
  debug: true  # 生产环境建议设置为false
  secret_key: "your-secret-key-here"  # 生产环境请使用强密钥
  allowed_hosts:
    - "*"  # 生产环境建议指定具体域名
  timezone: "Asia/Shanghai"
  language_code: "zh-hans"

# 数据库配置
database:
  engine: "django.db.backends.mysql"
  name: "user_behavior_db"
  user: "root"
  password: "your-password"  # 请修改为实际密码
  host: "*************"      # 请修改为实际数据库地址
  port: 3306
  options:
    charset: "utf8mb4"
    init_command: "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'"
    isolation_level: "read committed"
  test:
    charset: "utf8mb4"
    collation: "utf8mb4_unicode_ci"

# Elasticsearch配置
elasticsearch:
  hosts:
    - "your-es-host:9200"  # 请修改为实际ES地址
  username: "elastic"      # 请修改为实际用户名
  password: "your-password"  # 请修改为实际密码
  timeout: 180
  retry_on_timeout: true
  max_retries: 10
  use_ssl: false           # 如果使用HTTPS请设置为true
  verify_certs: false      # 如果使用HTTPS且需要验证证书请设置为true
  ca_certs: null           # 如果需要CA证书请指定路径
  sniff_on_start: true
  sniff_on_connection_fail: true
  sniffer_timeout: 60
  retry_on_status: [500, 502, 503, 504, 429]
  maxsize: 25
  block: true
  keep_alive: true
  keep_alive_timeout: 600
  
  # 索引配置
  indices:
    logs: "tgtweb_apioperatelog"  # 请根据实际索引名称修改
  
  # 查询配置
  query:
    size_limit: 10000
    scroll_timeout: "1m"
    aggs_size_limit: 1000

# Redis缓存配置
redis:
  host: "*************"    # 请修改为实际Redis地址
  port: 6379
  db: 1
  password: null           # 如果Redis有密码请设置

# JWT配置
jwt:
  access_token_lifetime_hours: 1    # 访问令牌有效期（小时）
  refresh_token_lifetime_days: 7    # 刷新令牌有效期（天）
  rotate_refresh_tokens: false
  blacklist_after_rotation: true
  algorithm: "HS256"
  auth_header_types: ["Bearer"]

# 日志配置
logging:
  level: "INFO"            # 日志级别: DEBUG, INFO, WARNING, ERROR
  max_bytes: 52428800      # 日志文件最大大小（50MB）
  backup_count: 10         # 保留的日志文件数量
  django:
    file: "logs/django-web.log"
    error_file: "logs/django-error.log"
  scheduler:
    file: "logs/scheduler/scheduler-service.log"

# REST Framework配置
rest_framework:
  page_size: 10            # 分页大小
  default_permission_classes:
    - "rest_framework.permissions.IsAuthenticated"

# CORS配置
cors:
  allow_all_origins: true  # 生产环境建议设置为false并指定具体域名

# 调度器配置
scheduler:
  daily_task_hour: 1       # 每日任务执行时间（小时，24小时制）
  daily_task_minute: 0     # 每日任务执行时间（分钟）
  check_interval_seconds: 60  # 调度器检查间隔（秒）

# 生产环境配置建议：
# 1. 设置 app.debug 为 false
# 2. 使用强密钥替换 app.secret_key
# 3. 限制 app.allowed_hosts 为具体域名
# 4. 设置正确的数据库连接信息
# 5. 配置正确的Elasticsearch集群信息
# 6. 如果使用HTTPS，设置相应的SSL配置
# 7. 设置Redis密码（如果需要）
# 8. 调整日志级别为WARNING或ERROR
# 9. 设置 cors.allow_all_origins 为 false
