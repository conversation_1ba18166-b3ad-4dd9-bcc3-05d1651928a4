# 用户行为分析系统配置文件

# 应用基础配置
app:
  debug: true
  secret_key: "django-insecure-your-secret-key-here"
  allowed_hosts:
    - "*"
  timezone: "Asia/Shanghai"
  language_code: "zh-hans"

# 数据库配置
database:
  engine: "django.db.backends.mysql"
  name: "user_behavior_db"
  user: "root"
  password: "tgt51848"
  host: "*************"
  port: 3306
  options:
    charset: "utf8mb4"
    init_command: "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'"
    isolation_level: "read committed"
  test:
    charset: "utf8mb4"
    collation: "utf8mb4_unicode_ci"

# Elasticsearch配置
elasticsearch:
  hosts:
    - "es-cn-x0r34fcdz0008x7a4.public.elasticsearch.aliyuncs.com:9200"
  username: "elastic"
  password: "Tgt51848"
  timeout: 180
  retry_on_timeout: true
  max_retries: 10
  use_ssl: false
  verify_certs: false
  ca_certs: null
  sniff_on_start: true
  sniff_on_connection_fail: true
  sniffer_timeout: 60
  retry_on_status: [500, 502, 503, 504, 429]
  maxsize: 25
  block: true
  keep_alive: true
  keep_alive_timeout: 600
  
  # 索引配置
  indices:
    logs: "tgtweb_apioperatelog"
  
  # 查询配置
  query:
    size_limit: 10000
    scroll_timeout: "1m"
    aggs_size_limit: 1000

# Redis缓存配置
redis:
  host: "*************"
  port: 6379
  db: 1
  password: null

# JWT配置
jwt:
  access_token_lifetime_hours: 1
  refresh_token_lifetime_days: 7
  rotate_refresh_tokens: false
  blacklist_after_rotation: true
  algorithm: "HS256"
  auth_header_types: ["Bearer"]

# 日志配置
logging:
  level: "INFO"
  max_bytes: 52428800  # 50MB
  backup_count: 10
  django:
    file: "logs/django-web.log"
    error_file: "logs/django-error.log"
  scheduler:
    file: "logs/scheduler/scheduler-service.log"

# REST Framework配置
rest_framework:
  page_size: 10
  default_permission_classes:
    - "rest_framework.permissions.IsAuthenticated"

# CORS配置
cors:
  allow_all_origins: true

# 调度器配置
scheduler:
  daily_task_hour: 1
  daily_task_minute: 0
  check_interval_seconds: 60
