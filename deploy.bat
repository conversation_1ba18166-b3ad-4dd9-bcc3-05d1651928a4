@echo off
REM 用户行为分析系统 - Windows一键部署脚本

setlocal enabledelayedexpansion

REM 默认参数
set CONTAINER_NAME=userbehavior-app
set IMAGE_TAG=userbehavior-app:latest
set PORT=8000
set CONF_DIR=.\conf
set LOGS_DIR=.\logs
set DATA_DIR=.\data

REM 解析命令行参数
:parse_args
if "%~1"=="" goto start_deploy
if "%~1"=="--name" (
    set CONTAINER_NAME=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--port" (
    set PORT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--help" goto show_help
if "%~1"=="-h" goto show_help
shift
goto parse_args

:show_help
echo 用法: %0 [选项]
echo 选项:
echo   --name CONTAINER_NAME    容器名称 (默认: userbehavior-app)
echo   --port PORT              端口映射 (默认: 8000)
echo   --help, -h               显示帮助信息
exit /b 0

:start_deploy
echo ==========================================
echo 用户行为分析系统 - Docker部署
echo ==========================================
echo 容器名称: %CONTAINER_NAME%
echo 端口映射: %PORT%
echo 配置目录: %CONF_DIR%
echo 日志目录: %LOGS_DIR%
echo 数据目录: %DATA_DIR%
echo ==========================================

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未运行，请启动Docker Desktop
    pause
    exit /b 1
)

echo [SUCCESS] Docker环境检查通过

REM 创建必要的目录
echo [INFO] 创建必要的目录...
if not exist "%CONF_DIR%" mkdir "%CONF_DIR%"
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%"

REM 检查配置文件
set CONFIG_FILE=%CONF_DIR%\config.yml
if not exist "%CONFIG_FILE%" (
    echo [WARNING] 配置文件不存在: %CONFIG_FILE%
    if exist "config.example.yml" (
        echo [INFO] 复制示例配置文件...
        copy "config.example.yml" "%CONFIG_FILE%" >nul
        echo [SUCCESS] 已复制示例配置到: %CONFIG_FILE%
        echo [WARNING] 请根据实际环境修改配置文件
    ) else (
        echo [WARNING] 将使用容器内默认配置
    )
) else (
    echo [SUCCESS] 配置文件已存在: %CONFIG_FILE%
)

REM 停止并删除现有容器
echo [INFO] 检查现有容器...
docker ps -a --format "table {{.Names}}" | findstr /r "^%CONTAINER_NAME%$" >nul 2>&1
if not errorlevel 1 (
    echo [INFO] 停止现有容器: %CONTAINER_NAME%
    docker stop "%CONTAINER_NAME%" >nul 2>&1
    echo [INFO] 删除现有容器: %CONTAINER_NAME%
    docker rm "%CONTAINER_NAME%" >nul 2>&1
)

REM 构建Docker镜像
echo [INFO] 构建Docker镜像: %IMAGE_TAG%
docker build -t "%IMAGE_TAG%" .
if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] 镜像构建成功

REM 运行容器
echo [INFO] 启动容器: %CONTAINER_NAME%
docker run -d ^
    --name %CONTAINER_NAME% ^
    -p %PORT%:8000 ^
    -e CONFIG_FILE_PATH=/app/conf/config.yml ^
    -v "%cd%\%CONF_DIR%":/app/conf ^
    -v "%cd%\%LOGS_DIR%":/app/logs ^
    -v "%cd%\%DATA_DIR%":/app/data ^
    %IMAGE_TAG%

if errorlevel 1 (
    echo [ERROR] 容器启动失败
    pause
    exit /b 1
)
echo [SUCCESS] 容器启动成功

REM 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查容器状态
docker ps --format "table {{.Names}}\t{{.Status}}" | findstr "%CONTAINER_NAME%.*Up" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 容器启动异常
    echo [INFO] 查看容器日志:
    docker logs "%CONTAINER_NAME%"
    pause
    exit /b 1
)
echo [SUCCESS] 容器运行正常

REM 健康检查
echo [INFO] 执行健康检查...
curl -f "http://localhost:%PORT%/api/analytics/health/" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 健康检查失败，服务可能还在启动中
) else (
    echo [SUCCESS] 健康检查通过
)

REM 显示部署信息
echo.
echo ==========================================
echo [SUCCESS] 部署完成！
echo ==========================================
echo 🌐 服务访问地址: http://localhost:%PORT%
echo 📋 API文档: http://localhost:%PORT%/api/
echo 🔍 健康检查: http://localhost:%PORT%/api/analytics/health/
echo.
echo 📁 映射目录:
echo   配置文件: %cd%\%CONF_DIR%
echo   日志文件: %cd%\%LOGS_DIR%
echo   数据文件: %cd%\%DATA_DIR%
echo.
echo 📝 常用命令:
echo   查看容器状态: docker ps
echo   查看容器日志: docker logs -f %CONTAINER_NAME%
echo   停止容器: docker stop %CONTAINER_NAME%
echo   重启容器: docker restart %CONTAINER_NAME%
echo.
echo ⚙️ 配置文件: %CONFIG_FILE%
echo   修改配置后执行: docker restart %CONTAINER_NAME%
echo ==========================================

pause
