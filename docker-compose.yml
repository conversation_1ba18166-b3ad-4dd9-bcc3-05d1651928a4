version: '3.8'

services:
  userbehavior-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: userbehavior-app
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    volumes:
      # 挂载日志目录，便于查看日志
      - ./logs:/app/logs
      # 挂载数据目录（如果需要持久化数据）
      - ./data:/app/data
    restart: unless-stopped
    networks:
      - userbehavior-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/analytics/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  userbehavior-network:
    driver: bridge

# 如果需要本地数据库和Redis，可以取消注释以下内容
# services:
#   mysql:
#     image: mysql:5.7
#     container_name: userbehavior-mysql
#     environment:
#       MYSQL_ROOT_PASSWORD: tgt51848
#       MYSQL_DATABASE: user_behavior_db
#       MYSQL_USER: app_user
#       MYSQL_PASSWORD: tgt51848
#     ports:
#       - "3306:3306"
#     volumes:
#       - mysql_data:/var/lib/mysql
#     networks:
#       - userbehavior-network
#     restart: unless-stopped
#
#   redis:
#     image: redis:6-alpine
#     container_name: userbehavior-redis
#     ports:
#       - "6379:6379"
#     networks:
#       - userbehavior-network
#     restart: unless-stopped
#
# volumes:
#   mysql_data:
