# 配置文件路径指定使用示例

本文档展示如何像Java Spring Boot一样指定配置文件路径。

## 🎯 支持的配置方式

我们的系统支持多种方式指定配置文件路径，优先级从高到低：

1. **直接传入路径** (最高优先级)
2. **环境变量 CONFIG_FILE_PATH**
3. **环境变量 SPRING_CONFIG_LOCATION** (兼容Spring Boot)
4. **默认路径** `config.yml` (最低优先级)

## 📋 使用方法

### 方法一：命令行参数 (推荐)

```bash
# 基本用法
python run_server_with_config.py --config /data/conf/config.yml

# 指定端口
python run_server_with_config.py --config /data/conf/9997/config.yml --port 9997

# 指定主机和端口
python run_server_with_config.py \
  --config /data/conf/config.yml \
  --host 0.0.0.0 \
  --port 8080

# Spring Boot风格
python run_server_with_config.py --spring-config-location=/data/conf/config.yml

# 简短参数
python run_server_with_config.py -c /data/conf/config.yml -p 9997
```

### 方法二：环境变量

```bash
# 设置环境变量
export CONFIG_FILE_PATH=/data/conf/config.yml
python run_server.py

# 或者Spring Boot风格
export SPRING_CONFIG_LOCATION=/data/conf/config.yml
python run_server.py

# Windows环境
set CONFIG_FILE_PATH=C:\data\conf\config.yml
python run_server.py
```

### 方法三：Docker部署

```bash
# 使用环境变量
docker run -d \
  --name userbehavior-app \
  -p 8000:8000 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v /host/path/config.yml:/app/conf/config.yml \
  userbehavior-app:latest

# 多端口部署
docker run -d \
  --name userbehavior-app-9997 \
  -p 9997:8000 \
  -e CONFIG_FILE_PATH=/app/conf/9997/config.yml \
  -v /data/conf/9997/config.yml:/app/conf/9997/config.yml \
  userbehavior-app:latest

# Spring Boot风格
docker run -d \
  --name userbehavior-app \
  -p 8000:8000 \
  -e SPRING_CONFIG_LOCATION=/app/conf/config.yml \
  -v /host/path/config.yml:/app/conf/config.yml \
  userbehavior-app:latest
```

## 🏗️ 实际部署场景

### 场景一：多环境部署

```bash
# 目录结构
/data/conf/
├── development/
│   └── config.yml
├── testing/
│   └── config.yml
└── production/
    └── config.yml

# 开发环境
python run_server_with_config.py --config /data/conf/development/config.yml --port 8000

# 测试环境
python run_server_with_config.py --config /data/conf/testing/config.yml --port 8001

# 生产环境
python run_server_with_config.py --config /data/conf/production/config.yml --port 8080
```

### 场景二：多端口部署

```bash
# 端口9997配置
python run_server_with_config.py --config /data/conf/9997/config.yml --port 9997

# 端口9998配置
python run_server_with_config.py --config /data/conf/9998/config.yml --port 9998

# 端口9999配置
python run_server_with_config.py --config /data/conf/9999/config.yml --port 9999
```

### 场景三：Docker集群部署

```bash
# 服务1
docker run -d \
  --name userbehavior-service-1 \
  -p 9997:8000 \
  -e CONFIG_FILE_PATH=/app/conf/service1.yml \
  -v /data/conf/service1.yml:/app/conf/service1.yml \
  userbehavior-app:latest

# 服务2
docker run -d \
  --name userbehavior-service-2 \
  -p 9998:8000 \
  -e CONFIG_FILE_PATH=/app/conf/service2.yml \
  -v /data/conf/service2.yml:/app/conf/service2.yml \
  userbehavior-app:latest
```

## 📝 配置文件示例

### 开发环境配置 (development.yml)

```yaml
app:
  debug: true
  secret_key: "dev-secret-key"
  allowed_hosts: ["*"]

database:
  host: "localhost"
  port: 3306
  name: "dev_user_behavior_db"
  user: "dev_user"
  password: "dev_password"

elasticsearch:
  hosts: ["localhost:9200"]
  username: ""
  password: ""

redis:
  host: "localhost"
  port: 6379
  db: 0

logging:
  level: "DEBUG"
```

### 生产环境配置 (production.yml)

```yaml
app:
  debug: false
  secret_key: "${SECRET_KEY}"  # 从环境变量读取
  allowed_hosts: ["yourdomain.com", "www.yourdomain.com"]

database:
  host: "prod-mysql-host"
  port: 3306
  name: "prod_user_behavior_db"
  user: "prod_user"
  password: "${DB_PASSWORD}"  # 从环境变量读取

elasticsearch:
  hosts: ["es-cluster-1:9200", "es-cluster-2:9200"]
  username: "elastic"
  password: "${ES_PASSWORD}"  # 从环境变量读取
  use_ssl: true
  verify_certs: true

redis:
  host: "prod-redis-host"
  port: 6379
  db: 1
  password: "${REDIS_PASSWORD}"  # 从环境变量读取

logging:
  level: "WARNING"
```

## 🔧 启动脚本示例

### 开发环境启动脚本 (start_dev.sh)

```bash
#!/bin/bash
export CONFIG_FILE_PATH=/data/conf/development/config.yml
export SECRET_KEY="dev-secret-key-12345"
export DB_PASSWORD="dev_password"

python run_server_with_config.py --port 8000
```

### 生产环境启动脚本 (start_prod.sh)

```bash
#!/bin/bash
export CONFIG_FILE_PATH=/data/conf/production/config.yml
export SECRET_KEY="$(cat /etc/secrets/secret_key)"
export DB_PASSWORD="$(cat /etc/secrets/db_password)"
export ES_PASSWORD="$(cat /etc/secrets/es_password)"
export REDIS_PASSWORD="$(cat /etc/secrets/redis_password)"

python run_server_with_config.py --port 8080
```

### Docker Compose示例

```yaml
version: '3.8'

services:
  userbehavior-dev:
    build: .
    ports:
      - "8000:8000"
    environment:
      - CONFIG_FILE_PATH=/app/conf/development.yml
      - SECRET_KEY=dev-secret-key
    volumes:
      - ./config/development.yml:/app/conf/development.yml

  userbehavior-prod:
    build: .
    ports:
      - "8080:8000"
    environment:
      - CONFIG_FILE_PATH=/app/conf/production.yml
      - SECRET_KEY_FILE=/run/secrets/secret_key
    volumes:
      - ./config/production.yml:/app/conf/production.yml
    secrets:
      - secret_key

secrets:
  secret_key:
    file: ./secrets/secret_key.txt
```

## 🚀 快速部署命令

### Java风格部署命令

```bash
# 类似 java -Dspring.config.location=/data/conf/config.yml -jar app.jar
python run_server_with_config.py --spring-config-location=/data/conf/9997/config.yml --port 9997

# 类似 java -Dserver.port=9997 -Dspring.config.location=/data/conf/config.yml -jar app.jar
python run_server_with_config.py \
  --spring-config-location=/data/conf/9997/config.yml \
  --port 9997 \
  --host 0.0.0.0
```

### 一键部署脚本

```bash
#!/bin/bash
# deploy.sh

CONFIG_PATH=${1:-/data/conf/config.yml}
PORT=${2:-8000}
HOST=${3:-0.0.0.0}

echo "部署配置:"
echo "  配置文件: $CONFIG_PATH"
echo "  端口: $PORT"
echo "  主机: $HOST"

python run_server_with_config.py \
  --config "$CONFIG_PATH" \
  --port "$PORT" \
  --host "$HOST"
```

使用方法：
```bash
# 使用默认配置
./deploy.sh

# 指定配置文件
./deploy.sh /data/conf/9997/config.yml

# 指定配置文件和端口
./deploy.sh /data/conf/9997/config.yml 9997

# 指定所有参数
./deploy.sh /data/conf/9997/config.yml 9997 *************
```

## 📊 配置验证

启动时会显示配置加载信息：

```
============================================================
Django服务器启动脚本 - 支持自定义配置文件
============================================================
使用配置文件: /data/conf/9997/config.yml
正在应用兼容性补丁...
✓ collections.abc模块已导入
✓ PyMySQL已安装为MySQLdb
✓ Django MySQL补丁已应用
✓ 日志配置已加载
✓ Django环境已成功设置
✓ 配置文件已加载: /data/conf/9997/config.yml
  - 数据库主机: *************
  - ES主机: es-cn-x0r34fcdz0008x7a4.public.elasticsearch.aliyuncs.com:9200
✓ Elasticsearch客户端已初始化
============================================================
启动Django服务器...
服务器地址: http://0.0.0.0:9997
当前工作目录: /app
按 Ctrl+C 停止服务器
============================================================
```

这样就完全实现了类似Java Spring Boot的配置文件管理方式！
