# 配置文件说明

本系统使用YAML格式的配置文件来管理所有配置项，类似于Java项目的application.yml文件。

## 配置文件位置

- **主配置文件**: `config.yml`
- **示例配置文件**: `config.example.yml`

## 配置文件结构

### 1. 应用基础配置 (app)

```yaml
app:
  debug: true                    # 调试模式，生产环境建议设为false
  secret_key: "your-secret-key"  # Django密钥，生产环境必须修改
  allowed_hosts:                 # 允许的主机名
    - "*"                        # 生产环境建议指定具体域名
  timezone: "Asia/Shanghai"      # 时区设置
  language_code: "zh-hans"       # 语言代码
```

### 2. 数据库配置 (database)

```yaml
database:
  engine: "django.db.backends.mysql"  # 数据库引擎
  name: "user_behavior_db"             # 数据库名
  user: "root"                         # 用户名
  password: "your-password"            # 密码
  host: "*************"               # 主机地址
  port: 3306                          # 端口
  options:                            # 数据库选项
    charset: "utf8mb4"
    init_command: "SET sql_mode='...'"
    isolation_level: "read committed"
  test:                               # 测试数据库配置
    charset: "utf8mb4"
    collation: "utf8mb4_unicode_ci"
```

### 3. Elasticsearch配置 (elasticsearch)

```yaml
elasticsearch:
  hosts:                              # ES集群地址列表
    - "es-host:9200"
  username: "elastic"                 # 用户名
  password: "your-password"           # 密码
  timeout: 180                        # 连接超时时间（秒）
  retry_on_timeout: true              # 超时重试
  max_retries: 10                     # 最大重试次数
  use_ssl: false                      # 是否使用SSL
  verify_certs: false                 # 是否验证证书
  ca_certs: null                      # CA证书路径
  sniff_on_start: true                # 启动时集群发现
  sniff_on_connection_fail: true      # 连接失败时集群发现
  sniffer_timeout: 60                 # 发现超时时间
  retry_on_status: [500, 502, 503, 504, 429]  # 重试状态码
  maxsize: 25                         # 连接池大小
  block: true                         # 连接池阻塞
  keep_alive: true                    # 保持连接
  keep_alive_timeout: 600             # 连接保持时间
  
  indices:                            # 索引配置
    logs: "tgtweb_apioperatelog"      # 日志索引名
  
  query:                              # 查询配置
    size_limit: 10000                 # 查询结果限制
    scroll_timeout: "1m"              # 滚动查询超时
    aggs_size_limit: 1000             # 聚合结果限制
```

### 4. Redis配置 (redis)

```yaml
redis:
  host: "*************"               # Redis主机地址
  port: 6379                          # Redis端口
  db: 1                               # 数据库编号
  password: null                      # 密码（可选）
```

### 5. JWT配置 (jwt)

```yaml
jwt:
  access_token_lifetime_hours: 1      # 访问令牌有效期（小时）
  refresh_token_lifetime_days: 7      # 刷新令牌有效期（天）
  rotate_refresh_tokens: false        # 是否轮换刷新令牌
  blacklist_after_rotation: true      # 轮换后加入黑名单
  algorithm: "HS256"                  # 加密算法
  auth_header_types: ["Bearer"]       # 认证头类型
```

### 6. 日志配置 (logging)

```yaml
logging:
  level: "INFO"                       # 日志级别
  max_bytes: 52428800                 # 日志文件最大大小（字节）
  backup_count: 10                    # 保留的日志文件数量
  django:
    file: "logs/django-web.log"       # Django日志文件
    error_file: "logs/django-error.log"  # Django错误日志文件
  scheduler:
    file: "logs/scheduler/scheduler-service.log"  # 调度器日志文件
```

### 7. REST Framework配置 (rest_framework)

```yaml
rest_framework:
  page_size: 10                       # 分页大小
  default_permission_classes:         # 默认权限类
    - "rest_framework.permissions.IsAuthenticated"
```

### 8. CORS配置 (cors)

```yaml
cors:
  allow_all_origins: true             # 是否允许所有来源
```

### 9. 调度器配置 (scheduler)

```yaml
scheduler:
  daily_task_hour: 1                  # 每日任务执行时间（小时）
  daily_task_minute: 0                # 每日任务执行时间（分钟）
  check_interval_seconds: 60          # 检查间隔（秒）
```

## 配置文件使用

### 1. 初始化配置

```bash
# 复制示例配置文件
cp config.example.yml config.yml

# 编辑配置文件
vim config.yml
```

### 2. 配置验证

系统启动时会自动验证配置文件：

- 如果配置文件不存在，使用默认配置
- 如果配置文件格式错误，使用默认配置并显示警告
- 配置加载成功会显示确认信息

### 3. 配置热重载

目前系统不支持配置热重载，修改配置后需要重启服务：

```bash
# Docker环境
docker stop userbehavior-app
docker rm userbehavior-app
python build_docker.py build-run

# 本地环境
# 重启Django服务和调度器服务
```

## 环境特定配置

### 开发环境

```yaml
app:
  debug: true
  allowed_hosts: ["*"]

logging:
  level: "DEBUG"

cors:
  allow_all_origins: true
```

### 生产环境

```yaml
app:
  debug: false
  secret_key: "strong-random-secret-key"
  allowed_hosts: ["yourdomain.com", "www.yourdomain.com"]

elasticsearch:
  use_ssl: true
  verify_certs: true

redis:
  password: "strong-redis-password"

logging:
  level: "WARNING"

cors:
  allow_all_origins: false
```

## 配置最佳实践

### 1. 安全配置

- 生产环境必须修改`app.secret_key`
- 限制`app.allowed_hosts`为具体域名
- 设置数据库和Redis密码
- 启用HTTPS时配置SSL相关选项

### 2. 性能配置

- 根据服务器资源调整ES连接池大小
- 设置合适的超时时间
- 调整日志级别减少I/O开销

### 3. 监控配置

- 设置合适的日志级别和文件大小
- 配置日志轮转避免磁盘空间不足
- 监控ES和Redis连接状态

### 4. 备份配置

- 定期备份配置文件
- 使用版本控制管理配置变更
- 为不同环境维护不同的配置文件

## 故障排除

### 1. 配置文件格式错误

```
错误: 配置文件格式错误 scanner.py:..., 使用默认配置
```

**解决方案**: 检查YAML语法，确保缩进正确

### 2. 配置项缺失

系统会使用默认值，但建议补全所有配置项

### 3. 数据库连接失败

检查数据库配置中的主机地址、端口、用户名和密码

### 4. ES连接超时

调整`elasticsearch.timeout`和`elasticsearch.max_retries`配置

## 配置文件模板

系统提供了完整的配置文件模板`config.example.yml`，包含所有配置项的说明和建议值。
