# Docker部署指南

本文档介绍如何使用Docker容器化部署用户行为分析系统。

## 系统要求

- Docker 20.10+
- Docker Compose 1.29+
- 至少2GB可用内存
- 至少5GB可用磁盘空间

## 快速开始

### 1. 构建Docker镜像

```bash
# 在项目根目录下执行
docker build -t userbehavior-app:latest .
```

### 2. 使用Docker Compose启动

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 3. 直接使用Docker运行

```bash
# 运行容器
docker run -d \
  --name userbehavior-app \
  -p 8000:8000 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:latest

# 查看日志
docker logs -f userbehavior-app

# 停止容器
docker stop userbehavior-app
docker rm userbehavior-app
```

## 配置说明

### 环境变量

容器支持以下环境变量：

- `PYTHONUNBUFFERED=1`: 禁用Python输出缓冲
- `TZ=Asia/Shanghai`: 设置时区

### 端口映射

- `8000`: Django Web服务端口

### 数据卷挂载

- `./logs:/app/logs`: 日志文件目录
- `./data:/app/data`: 数据文件目录

## 服务说明

容器启动后会自动运行两个服务：

1. **Django Web服务**: 监听8000端口，提供API接口
2. **调度器服务**: 后台运行，每天凌晨1:00执行数据处理任务

## 健康检查

容器配置了健康检查，会定期检查服务状态：

```bash
# 检查容器健康状态
docker ps

# 查看健康检查详情
docker inspect userbehavior-app | grep -A 10 Health
```

## 日志管理

### 查看日志

```bash
# 查看容器日志
docker logs userbehavior-app

# 查看Django服务日志
docker exec userbehavior-app tail -f /app/logs/django-web.log

# 查看调度器日志
docker exec userbehavior-app tail -f /app/logs/scheduler/scheduler-service.log

# 查看Docker启动日志
docker exec userbehavior-app tail -f /app/logs/docker_start.log
```

### 日志文件位置

- Django Web服务: `/app/logs/django-web.log`
- Django错误日志: `/app/logs/django-error.log`
- 调度器服务: `/app/logs/scheduler/scheduler-service.log`
- Docker启动日志: `/app/logs/docker_start.log`

## 故障排除

### 1. 容器启动失败

```bash
# 查看容器日志
docker logs userbehavior-app

# 进入容器调试
docker exec -it userbehavior-app /bin/bash
```

### 2. 服务无法访问

```bash
# 检查端口映射
docker port userbehavior-app

# 检查网络连接
curl http://localhost:8000/api/analytics/health/
```

### 3. 数据库连接问题

确保数据库服务可访问，检查配置文件中的数据库连接信息。

### 4. Elasticsearch连接问题

确保Elasticsearch服务可访问，检查配置文件中的ES连接信息。

## 生产环境部署

### 1. 使用外部数据库

修改`src/config/settings.py`中的数据库配置，指向外部MySQL服务。

### 2. 使用外部Redis

修改`src/config/settings.py`中的Redis配置，指向外部Redis服务。

### 3. 使用外部Elasticsearch

修改`src/config/settings.py`中的Elasticsearch配置，指向外部ES集群。

### 4. 配置反向代理

建议在生产环境中使用Nginx作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 更新部署

### 1. 重新构建镜像

```bash
# 停止现有容器
docker-compose down

# 重新构建镜像
docker-compose build

# 启动新容器
docker-compose up -d
```

### 2. 滚动更新

```bash
# 构建新镜像
docker build -t userbehavior-app:v2 .

# 停止旧容器
docker stop userbehavior-app

# 启动新容器
docker run -d \
  --name userbehavior-app-new \
  -p 8000:8000 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:v2

# 删除旧容器
docker rm userbehavior-app
docker rename userbehavior-app-new userbehavior-app
```

## 监控和维护

### 1. 资源监控

```bash
# 查看容器资源使用情况
docker stats userbehavior-app

# 查看容器详细信息
docker inspect userbehavior-app
```

### 2. 备份数据

```bash
# 备份日志文件
tar -czf logs-backup-$(date +%Y%m%d).tar.gz logs/

# 备份数据文件
tar -czf data-backup-$(date +%Y%m%d).tar.gz data/
```

### 3. 清理资源

```bash
# 清理未使用的镜像
docker image prune

# 清理未使用的容器
docker container prune

# 清理未使用的卷
docker volume prune
```
