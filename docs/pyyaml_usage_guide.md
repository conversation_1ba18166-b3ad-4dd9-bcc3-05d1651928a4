# PyYAML使用指南

本文档详细介绍PyYAML的使用方法和在项目中的配置文件管理。

## PyYAML简介

PyYAML是Python的YAML解析器和生成器。YAML（YAML Ain't Markup Language）是一种人类可读的数据序列化标准，常用于配置文件。

## 安装PyYAML

```bash
# 使用pip安装
pip install PyYAML==6.0

# 使用国内镜像源
pip install PyYAML==6.0 -i https://mirrors.aliyun.com/pypi/simple/

# 使用conda安装
conda install pyyaml=6.0
```

## 基本使用方法

### 1. 导入模块

```python
import yaml
```

### 2. 读取YAML文件

```python
# 读取YAML文件
with open('config.yml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

print(config)
```

### 3. 写入YAML文件

```python
# 数据结构
data = {
    'database': {
        'host': 'localhost',
        'port': 3306,
        'name': 'mydb'
    },
    'features': ['feature1', 'feature2']
}

# 写入YAML文件
with open('output.yml', 'w', encoding='utf-8') as f:
    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
```

### 4. 字符串与YAML互转

```python
# 字符串转YAML对象
yaml_string = """
database:
  host: localhost
  port: 3306
"""
config = yaml.safe_load(yaml_string)

# YAML对象转字符串
yaml_string = yaml.dump(config, default_flow_style=False)
```

## 项目中的配置文件管理

### 1. 配置文件路径指定

我们的系统支持多种方式指定配置文件路径：

#### 方法一：环境变量

```bash
# 设置配置文件路径
export CONFIG_FILE_PATH=/data/conf/config.yml

# 或使用Spring Boot风格
export SPRING_CONFIG_LOCATION=/data/conf/config.yml

# 启动服务
python run_server.py
```

#### 方法二：命令行参数

```bash
# 使用--config参数
python run_server_with_config.py --config /data/conf/config.yml

# 使用Spring Boot风格参数
python run_server_with_config.py --spring-config-location=/data/conf/config.yml

# 同时指定主机和端口
python run_server_with_config.py --config /data/conf/config.yml --host 0.0.0.0 --port 9997
```

#### 方法三：Docker环境变量

```bash
# Docker运行时指定配置文件
docker run -d \
  --name userbehavior-app \
  -p 8000:8000 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v /host/path/config.yml:/app/conf/config.yml \
  userbehavior-app:latest
```

### 2. 配置文件结构

```yaml
# config.yml 示例
app:
  debug: true
  secret_key: "your-secret-key"
  allowed_hosts:
    - "*"

database:
  engine: "django.db.backends.mysql"
  name: "user_behavior_db"
  user: "root"
  password: "password"
  host: "*************"
  port: 3306
  options:
    charset: "utf8mb4"

elasticsearch:
  hosts:
    - "es-host:9200"
  username: "elastic"
  password: "password"
  timeout: 180

redis:
  host: "redis-host"
  port: 6379
  db: 1
```

### 3. 配置读取示例

```python
from src.config.config_loader import config

# 读取简单配置
debug_mode = config.get('app.debug', False)
secret_key = config.get('app.secret_key', 'default-key')

# 读取复杂配置
db_config = config.get_database_config()
es_config = config.get_elasticsearch_config()
redis_config = config.get_redis_config()

# 读取列表配置
allowed_hosts = config.get('app.allowed_hosts', ['localhost'])
es_hosts = config.get('elasticsearch.hosts', ['localhost:9200'])
```

## 高级用法

### 1. 配置文件继承

```yaml
# base.yml
defaults: &defaults
  timeout: 30
  retries: 3

development:
  <<: *defaults
  debug: true

production:
  <<: *defaults
  debug: false
```

### 2. 环境变量替换

```python
import os
import yaml

def load_config_with_env(file_path):
    """加载配置文件并替换环境变量"""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 替换环境变量
    content = os.path.expandvars(content)
    
    return yaml.safe_load(content)

# 配置文件中使用环境变量
# database:
#   host: ${DB_HOST}
#   password: ${DB_PASSWORD}
```

### 3. 配置验证

```python
import yaml
from cerberus import Validator

# 定义配置模式
schema = {
    'database': {
        'type': 'dict',
        'schema': {
            'host': {'type': 'string', 'required': True},
            'port': {'type': 'integer', 'min': 1, 'max': 65535},
            'name': {'type': 'string', 'required': True}
        }
    }
}

def validate_config(config_data):
    """验证配置文件"""
    validator = Validator(schema)
    if not validator.validate(config_data):
        raise ValueError(f"配置验证失败: {validator.errors}")
    return True
```

## 最佳实践

### 1. 安全配置

```yaml
# 生产环境配置
app:
  debug: false
  secret_key: "${SECRET_KEY}"  # 从环境变量读取

database:
  password: "${DB_PASSWORD}"   # 敏感信息从环境变量读取

# 不要在配置文件中硬编码密码
```

### 2. 环境分离

```bash
# 不同环境使用不同配置文件
config/
├── config.yml          # 默认配置
├── development.yml     # 开发环境
├── testing.yml         # 测试环境
└── production.yml      # 生产环境

# 启动时指定环境配置
python run_server_with_config.py --config config/production.yml
```

### 3. 配置文件版本控制

```bash
# .gitignore
config.yml              # 忽略本地配置
config.local.yml        # 忽略本地配置

# 保留模板文件
!config.example.yml     # 保留示例配置
!config.template.yml    # 保留模板配置
```

## 故障排除

### 1. YAML语法错误

```python
try:
    with open('config.yml', 'r') as f:
        config = yaml.safe_load(f)
except yaml.YAMLError as e:
    print(f"YAML语法错误: {e}")
```

### 2. 文件编码问题

```python
# 确保使用UTF-8编码
with open('config.yml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)
```

### 3. 配置项缺失

```python
# 使用默认值
database_host = config.get('database.host', 'localhost')

# 或抛出异常
if 'database.host' not in config:
    raise ValueError("数据库主机配置缺失")
```

## 实际使用示例

### 启动命令示例

```bash
# 开发环境
python run_server_with_config.py --config config/development.yml --port 8000

# 测试环境
python run_server_with_config.py --config config/testing.yml --port 8001

# 生产环境
python run_server_with_config.py --config /data/conf/9997/config.yml --port 9997

# 使用环境变量
export CONFIG_FILE_PATH=/data/conf/9997/config.yml
python run_server.py

# Docker部署
docker run -d \
  --name userbehavior-app-9997 \
  -p 9997:8000 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v /data/conf/9997/config.yml:/app/conf/config.yml \
  userbehavior-app:latest
```

这样就实现了类似Java Spring Boot的配置文件管理方式！
