#!/usr/bin/env python
# 简单调度器

import os
import sys
import time
import logging
import logging.handlers
from datetime import datetime, timedelta
import subprocess

# 禁用Django的日志配置
os.environ['DISABLE_DJANGO_LOGGING'] = 'True'

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.config.settings')

# 导入配置加载器
try:
    from src.config.config_loader import config
    print("配置加载器已导入")
except ImportError:
    print("警告：无法导入配置加载器，使用默认配置")
    config = None

# 创建日志目录
base_dir = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(base_dir, 'logs', 'scheduler')
os.makedirs(logs_dir, exist_ok=True)

# 配置日志
def configure_logging():
    """配置日志"""
    # 完全重置日志系统，确保与Django日志系统完全隔离
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.WARNING)  # 设置根日志级别为WARNING，减少干扰

    # 清除根日志记录器的所有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建独立的调度器日志记录器
    logger = logging.getLogger('scheduler')
    logger.setLevel(logging.INFO)
    logger.propagate = False  # 完全禁止日志传播到根日志记录器

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 文件处理器 - 使用独立的调度器日志文件，避免与Django日志冲突
    log_file = os.path.join(logs_dir, 'scheduler-service.log')
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=5,
        encoding='utf-8',
        delay=True  # 延迟创建文件，减少冲突
    )
    file_handler.setLevel(logging.INFO)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到调度器日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 禁用其他可能的日志记录器，确保完全隔离
    logging.getLogger('django').setLevel(logging.CRITICAL)
    logging.getLogger('elasticsearch').setLevel(logging.CRITICAL)
    logging.getLogger('urllib3').setLevel(logging.CRITICAL)

    print(f"调度器日志已配置，日志文件保存在 {log_file}")
    print("已完全隔离Django日志系统")

    return logger

# 创建日志记录器
logger = configure_logging()

# 初始化ES客户端
try:
    # 先导入Django
    import django
    django.setup()

    # 初始化ES客户端
    from src.utils.es_client import es_client
    logger.info(f"Elasticsearch客户端已初始化，连接到: {es_client.transport.hosts}")
except Exception as e:
    logger.error(f"初始化ES客户端时出错: {e}")

def run_task():
    """运行任务"""
    logger.info("开始执行任务...")

    try:
        # 使用subprocess运行任务
        cmd = [sys.executable, 'run_task_with_all_systems.py']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        if process.returncode == 0:
            logger.info("任务执行成功")
            logger.info(f"输出: {stdout.decode('utf-8', errors='replace')}")
        else:
            logger.error("任务执行失败")
            logger.error(f"错误: {stderr.decode('utf-8', errors='replace')}")
    except Exception as e:
        logger.error(f"执行任务时出错: {e}")

def simple_scheduler():
    """简单调度器"""
    logger.info("启动简单调度器...")

    while True:
        try:
            # 获取当前时间
            now = datetime.now()
            logger.info(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")

            # 从配置文件读取执行时间，默认凌晨1:00
            target_hour = config.get('scheduler.daily_task_hour', 1) if config else 1
            target_minute = config.get('scheduler.daily_task_minute', 0) if config else 0

            # 检查是否到达执行时间
            if now.hour == target_hour and now.minute == target_minute:
                logger.info("到达执行时间，开始执行任务...")
                run_task()

                # 等待1分钟，避免重复执行
                logger.info("等待1分钟，避免重复执行...")
                time.sleep(60)
            else:
                # 计算距离下一次执行的时间
                next_run_time = datetime(now.year, now.month, now.day, target_hour, target_minute, 0)
                if now > next_run_time:
                    # 使用timedelta来增加1天，避免月底超出问题
                    next_day = now + timedelta(days=1)
                    next_run_time = datetime(next_day.year, next_day.month, next_day.day, target_hour, target_minute, 0)

                time_to_next_run = next_run_time - now
                logger.info(f"距离下一次执行的时间: {time_to_next_run}")

                # 从配置文件读取检查间隔，默认60秒
                check_interval = config.get('scheduler.check_interval_seconds', 60) if config else 60
                logger.info(f"等待{check_interval}秒...")
                time.sleep(check_interval)
        except Exception as e:
            logger.error(f"调度器出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 从配置文件读取检查间隔，默认60秒
            check_interval = config.get('scheduler.check_interval_seconds', 60) if config else 60
            logger.info(f"等待{check_interval}秒...")
            time.sleep(check_interval)

if __name__ == "__main__":
    print("启动简单调度器...")
    print("已禁用Django日志配置，使用独立的调度器日志配置")
    simple_scheduler()
