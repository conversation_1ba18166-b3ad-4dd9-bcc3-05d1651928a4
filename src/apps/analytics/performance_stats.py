#!/usr/bin/env python
# 性能统计分析API

import logging
import json
from datetime import datetime, timedelta
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from elasticsearch_dsl import Search, Q, A
from src.utils.es_client import es_client
from src.config.constants import ElasticsearchIndices

logger = logging.getLogger(__name__)

@api_view(['GET'])
def slow_functions(request):
    """
    获取耗时最多功能排行

    该接口用于查询指定时间范围内平均耗时大于500ms的所有功能，按平均耗时降序排序。
    时间范围最长为1天。

    查询参数:
    - start_time: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)（必填）
    - end_time: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)（必填）
    - min_avg_cost: 最小平均耗时，单位毫秒（选填，默认为500）
    - min_count: 最小请求次数，只返回请求次数大于等于这个数的结果（选填，默认为5）
    - page: 页码，从1开始（选填，默认为1）
    - page_size: 每页记录数（选填，默认为10，最大为100）

    返回:
    - data: 符合条件的功能耗时统计数据列表，每项包含requestPath（请求路径）、avgCost（平均耗时）、
      maxCost（最大耗时）、minCost（最小耗时）、count（请求次数）等信息
    - pagination: 分页信息，包含page（当前页码）、page_size（每页记录数）、
      total_count（总记录数）和total_pages（总页数）
    - summary: 查询摘要，包含查询参数和结果统计
    - message: 提示信息（仅当没有数据时显示）

    示例请求:
    GET /api/analytics/slow-functions/?start_time=2025-04-24%2000:00:00&end_time=2025-04-24%2023:59:59&min_avg_cost=500

    示例响应:
    {
        "data": [
            {
                "requestPath": "/api/reports/generate/",
                "avgCost": 2356.78,
                "maxCost": 5432.10,
                "minCost": 987.65,
                "count": 42,
                "totalCost": 98984.76
            },
            {
                "requestPath": "/api/analytics/log-details/",
                "avgCost": 1234.56,
                "maxCost": 3456.78,
                "minCost": 765.43,
                "count": 87,
                "totalCost": 107406.72
            },
            ...
        ],
        "pagination": {
            "page": 1,
            "page_size": 10,
            "total_count": 15,
            "total_pages": 2
        },
        "summary": {
            "start_time": "2025-04-24 00:00:00",
            "end_time": "2025-04-24 23:59:59",
            "min_avg_cost": 500,
            "min_count": 5,
            "total_functions": 15
        }
    }
    """
    try:
        # 获取必填参数
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')

        # 验证必填参数
        if not all([start_time, end_time]):
            return Response(
                {'error': '缺少必填参数: start_time, end_time 都是必填的'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 解析时间参数
        try:
            start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return Response(
                {'error': '时间格式不正确，正确格式为: YYYY-MM-DD HH:MM:SS'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证时间范围
        time_diff = end_datetime - start_datetime
        if time_diff.total_seconds() <= 0:
            return Response(
                {'error': '结束时间必须大于开始时间'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if time_diff.total_seconds() > 86400:  # 最多1天
            return Response(
                {'error': '时间范围最多为1天'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取选填参数
        try:
            min_avg_cost = float(request.query_params.get('min_avg_cost', '100'))
            if min_avg_cost < 0:
                min_avg_cost = 100
        except ValueError:
            min_avg_cost = 100

        try:
            min_count = int(request.query_params.get('min_count', '2'))
            if min_count < 1:
                min_count = 2
        except ValueError:
            min_count = 2

        # 获取分页参数
        try:
            page = int(request.query_params.get('page', '1'))
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        try:
            page_size = int(request.query_params.get('page_size', '10'))
            if page_size < 1:
                page_size = 10
            elif page_size > 100:  # 限制最大每页数量
                page_size = 100
        except ValueError:
            page_size = 10

        # 构建ES查询
        try:
            # 获取系统类型参数
            system_type = request.query_params.get('system_type', 'api')

            # 使用常量类中定义的日志索引
            log_index = ElasticsearchIndices.get_operation_log(system_type)
            logger.info(f"使用日志索引: {log_index}, 系统类型: {system_type}")

            # 获取字段映射
            field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

            # 构建基本查询
            s = Search(using=es_client, index=log_index)

            # 添加时间范围过滤
            s = s.filter('range', **{field_mapping['create_time']: {
                'gte': start_time,
                'lte': end_time
            }})

            # 添加耗时大于0的过滤条件（排除无效数据）
            s = s.filter('range', **{field_mapping['cost']: {'gt': 0}})

            # 打印查询结果的前几条，查看字段名称
            sample_query = Search(using=es_client, index=log_index)
            sample_query = sample_query.filter('range', **{field_mapping['create_time']: {
                'gte': start_time,
                'lte': end_time
            }})
            sample_query = sample_query.filter('range', **{field_mapping['cost']: {'gt': 0}})
            sample_query = sample_query.sort(f"-{field_mapping['cost']}")  # 按耗时降序排序
            sample_query = sample_query[:5]  # 只获取5条记录

            try:
                sample_results = sample_query.execute()
                logger.info(f"样本查询结果数量: {len(sample_results.hits)}")
                # 只在DEBUG级别输出详细的样本记录
                for hit in sample_results:
                    logger.debug(f"样本记录: {hit.to_dict()}")
            except Exception as e:
                logger.warning(f"样本查询失败: {str(e)}")

            # 添加按requestPath分组的聚合
            # 根据样本查询结果，确认字段名称正确
            # 对于不同的索引，字段类型可能不同
            # 尝试使用不同的字段名称进行聚合
            request_path_field = field_mapping['request_path']

            # 对于boss系统，直接使用requestUrl字段，不添加.keyword后缀
            if system_type == 'boss':
                agg_field = request_path_field
            else:
                # 对于api和oss系统，添加.keyword后缀
                agg_field = f"{request_path_field}.keyword"

            logger.info(f"使用聚合字段: {agg_field}")

            s.aggs.bucket('by_path', 'terms',
                field=agg_field,
                size=1000  # 获取足够多的分组
            ).metric('avg_cost', 'avg',
                field=field_mapping['cost']
            ).metric('max_cost', 'max',
                field=field_mapping['cost']
            ).metric('min_cost', 'min',
                field=field_mapping['cost']
            ).metric('total_cost', 'sum',
                field=field_mapping['cost']
            )

            # 设置size为0，因为我们只关心聚合结果
            s = s.extra(size=0)

            # 执行查询
            logger.debug(f"执行耗时功能排行查询: {s.to_dict()}")
            logger.info(f"开始执行耗时功能排行查询，时间范围: {start_time} 到 {end_time}")

            # 添加重试机制
            retry_count = 0
            max_retries = 3
            while retry_count < max_retries:
                try:
                    response = s.execute()
                    logger.info(f"查询执行成功，开始处理结果")
                    # 只在DEBUG级别输出详细的聚合结果
                    if hasattr(response, 'aggregations'):
                        logger.debug(f"聚合结果存在: {response.aggregations.to_dict()}")
                        logger.info(f"聚合结果包含 {len(response.aggregations.by_path.buckets)} 个功能路径")
                    else:
                        logger.warning("聚合结果不存在")
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise
                    logger.warning(f"执行查询失败，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    import time
                    time.sleep(2)  # 等待2秒后重试

            # 处理聚合结果
            function_stats = []

            if hasattr(response, 'aggregations') and hasattr(response.aggregations, 'by_path'):
                logger.info(f"by_path聚合存在，桶数量: {len(response.aggregations.by_path.buckets)}")

                # 如果没有聚合结果，使用样本数据构建演示结果
                if len(response.aggregations.by_path.buckets) == 0:
                    logger.info("没有聚合结果，使用样本数据构建演示结果")

                    # 使用样本查询中的数据构建结果
                    # 按requestPath分组汇总数据
                    path_stats = {}

                    try:
                        # 重新执行查询，使用聚合而不是限制记录数
                        # 这样可以统计时间范围内的所有数据
                        sample_query = Search(using=es_client, index=log_index)

                        # 首先执行一个简单的查询，检查索引中是否有数据
                        simple_query = Search(using=es_client, index=log_index)[:1]
                        try:
                            simple_result = simple_query.execute()
                            logger.info(f"简单查询结果数量: {len(simple_result.hits)}")
                            if len(simple_result.hits) > 0:
                                logger.info(f"简单查询结果示例: {simple_result.hits[0].to_dict()}")
                        except Exception as e:
                            logger.error(f"简单查询失败: {str(e)}")

                        # 添加时间范围过滤
                        sample_query = sample_query.filter('range', **{field_mapping['create_time']: {
                            'gte': start_time,
                            'lte': end_time
                        }})

                        # 添加耗时大于0的过滤条件
                        sample_query = sample_query.filter('range', **{field_mapping['cost']: {'gt': 0}})

                        # 执行一个简单的查询，检查过滤条件后是否有数据
                        filtered_query = sample_query[:1]
                        try:
                            filtered_result = filtered_query.execute()
                            logger.info(f"过滤后的查询结果数量: {len(filtered_result.hits)}")
                            if len(filtered_result.hits) > 0:
                                logger.info(f"过滤后的查询结果示例: {filtered_result.hits[0].to_dict()}")
                        except Exception as e:
                            logger.error(f"过滤后的查询失败: {str(e)}")

                        # 尝试不同的聚合方式
                        # 首先检查requestPath字段的类型
                        try:
                            mapping_query = es_client.indices.get_mapping(index=log_index)
                            logger.debug(f"索引映射信息: {mapping_query}")
                        except Exception as e:
                            logger.error(f"获取索引映射失败: {str(e)}")

                        # 尝试使用requestPath字段而不是requestPath.keyword
                        sample_query.aggs.bucket('by_path', 'terms',
                            field=field_mapping['request_path'],
                            size=10000  # 设置为足够大的值，确保获取所有分组
                        ).metric('avg_cost', 'avg',
                            field=field_mapping['cost']
                        ).metric('max_cost', 'max',
                            field=field_mapping['cost']
                        ).metric('min_cost', 'min',
                            field=field_mapping['cost']
                        ).metric('total_cost', 'sum',
                            field=field_mapping['cost']
                        )

                        # 设置size为0，因为我们只关心聚合结果
                        sample_query = sample_query.extra(size=0)

                        sample_results = sample_query.execute()
                        logger.info(f"扩展样本查询结果数量: {len(sample_results.hits)}")

                        # 处理聚合结果
                        if hasattr(sample_results, 'aggregations') and hasattr(sample_results.aggregations, 'by_path'):
                            logger.info(f"by_path聚合存在，桶数量: {len(sample_results.aggregations.by_path.buckets)}")

                            for bucket in sample_results.aggregations.by_path.buckets:
                                path = bucket.key
                                count = bucket.doc_count
                                avg_cost = bucket.avg_cost.value
                                max_cost = bucket.max_cost.value
                                min_cost = bucket.min_cost.value
                                total_cost = bucket.total_cost.value

                                # 只保留平均耗时大于min_avg_cost且请求次数大于min_count的结果
                                if avg_cost >= min_avg_cost and count >= min_count:
                                    path_stats[path] = {
                                        'costs': [min_cost, avg_cost, max_cost],  # 使用最小、平均、最大值作为样本
                                        'count': count,
                                        'total_cost': total_cost,
                                        'avg_cost': avg_cost,
                                        'max_cost': max_cost,
                                        'min_cost': min_cost
                                    }

                            logger.info(f"符合条件的路径数量: {len(path_stats)}")

                        # 不添加模拟数据，只返回真实查询结果
                        if len(path_stats) == 0:
                            logger.info("没有找到符合条件的数据")

                        # 将统计数据转换为返回格式
                        for path, stats in path_stats.items():
                            function_stats.append({
                                'requestPath': path,
                                'avgCost': round(stats.get('avg_cost', 0), 2),
                                'maxCost': round(stats.get('max_cost', 0), 2),
                                'minCost': round(stats.get('min_cost', 0), 2),
                                'count': stats['count'],
                                'totalCost': round(stats.get('total_cost', 0), 2)
                            })
                    except Exception as e:
                        logger.error(f"处理样本数据失败: {str(e)}")
                else:
                    # 如果有聚合结果，正常处理
                    for bucket in response.aggregations.by_path.buckets:
                        # 获取该路径的请求次数
                        count = bucket.doc_count

                        # 获取平均耗时
                        avg_cost = bucket.avg_cost.value

                        # 只保留平均耗时大于min_avg_cost且请求次数大于min_count的结果
                        # 只在DEBUG级别输出详细的聚合结果
                        logger.debug(f"聚合结果: path={bucket.key}, count={count}, avg_cost={avg_cost}")
                        if avg_cost >= min_avg_cost and count >= min_count:
                            function_stats.append({
                                'requestPath': bucket.key,
                                'avgCost': round(avg_cost, 2),
                                'maxCost': round(bucket.max_cost.value, 2),
                                'minCost': round(bucket.min_cost.value, 2),
                                'count': count,
                                'totalCost': round(bucket.total_cost.value, 2)
                            })

            # 按平均耗时降序排序
            function_stats.sort(key=lambda x: x['avgCost'], reverse=True)

            # 计算总数
            total_count = len(function_stats)

            # 分页处理
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size

            # 获取当前页的数据
            page_data = function_stats[start_idx:end_idx] if start_idx < total_count else []

            # 计算总页数
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            # 构建返回结果
            result = {
                'data': page_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages
                },
                'summary': {
                    'start_time': start_time,
                    'end_time': end_time,
                    'min_avg_cost': min_avg_cost,
                    'min_count': min_count,
                    'total_functions': total_count
                }
            }

            # 如果没有数据，添加提示信息
            if total_count == 0:
                result['message'] = '没有找到符合条件的数据。请检查查询参数或调整时间范围。'

            return Response(result)

        except Exception as e:
            logger.error(f"查询耗时功能排行失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return Response(
                {'error': f'查询失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response(
            {'error': f'处理请求失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
