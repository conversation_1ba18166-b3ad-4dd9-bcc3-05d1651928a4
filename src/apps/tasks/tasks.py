import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from django.db import transaction, connection
from django.utils import timezone
from elasticsearch_dsl import Search, Q
from elasticsearch import Elasticsearch
from django.conf import settings

# 导入ES客户端和常量
from src.utils.es_client import es_client
from src.config.constants import ElasticsearchIndices

# 导入用户行为分析模型
from src.apps.analytics.models import UserOperateAnalysisDay

logger = logging.getLogger(__name__)

def daily_aggregation_task(params):
    """
    每日数据聚合任务
    从Elasticsearch中提取前一天的数据，按不同维度汇总后存入daily_statistics表

    参数:
        params (dict): 任务参数，可包含自定义的日期范围等

    返回:
        dict: 包含任务执行结果的字典
    """
    try:
        # 获取处理日期，默认为昨天
        process_date = params.get('date')
        if not process_date:
            process_date = (timezone.now() - timedelta(days=1)).strftime('%Y-%m-%d')

        logger.info(f"开始执行每日数据聚合任务，处理日期: {process_date}")

        # 设置查询时间范围
        start_time = f"{process_date}T00:00:00Z"
        end_time = f"{process_date}T23:59:59Z"

        # 构建Elasticsearch查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD)\
            .filter('range', requestTime={'gte': start_time, 'lte': end_time})

        # 执行查询获取数据
        response = s.scan()

        # 将查询结果转换为DataFrame
        data = []
        for hit in response:
            data.append({
                'operateUserId': hit.operateUserId,
                'operateUserName': hit.operateUserNameAnalyzed,
                'requestUrl': hit.requestUrl,
                'requestTime': hit.requestTime,
                'costTime': hit.costTime,
                'operateResult': hit.operateResult,
                'ip': hit.ip
            })

        df = pd.DataFrame(data)

        if df.empty:
            logger.warning(f"日期 {process_date} 没有找到数据")
            return {"message": "没有找到数据", "affected_rows": 0}

        # 数据聚合处理
        results = []

        # 1. 用户维度聚合
        user_stats = df.groupby('operateUserId').agg({
            'requestUrl': 'count',  # 访问次数
            'costTime': ['mean', 'min', 'max'],  # 响应时间统计
        }).reset_index()

        for _, row in user_stats.iterrows():
            results.append({
                'stat_date': process_date,
                'dimension': 'user',
                'dimension_value': str(row['operateUserId']),
                'metric_name': 'visit_count',
                'metric_value': row[('requestUrl', 'count')]
            })
            results.append({
                'stat_date': process_date,
                'dimension': 'user',
                'dimension_value': str(row['operateUserId']),
                'metric_name': 'avg_response_time',
                'metric_value': row[('costTime', 'mean')]
            })

        # 2. 功能维度聚合
        feature_stats = df.groupby('requestUrl').agg({
            'operateUserId': 'nunique',  # 独立用户数
            'requestUrl': 'count',  # 访问次数
            'costTime': ['mean', 'min', 'max'],  # 响应时间统计
        }).reset_index()

        for _, row in feature_stats.iterrows():
            results.append({
                'stat_date': process_date,
                'dimension': 'feature',
                'dimension_value': row['requestUrl'],
                'metric_name': 'visit_count',
                'metric_value': row[('requestUrl', 'count')]
            })
            results.append({
                'stat_date': process_date,
                'dimension': 'feature',
                'dimension_value': row['requestUrl'],
                'metric_name': 'unique_users',
                'metric_value': row[('operateUserId', 'nunique')]
            })
            results.append({
                'stat_date': process_date,
                'dimension': 'feature',
                'dimension_value': row['requestUrl'],
                'metric_name': 'avg_response_time',
                'metric_value': row[('costTime', 'mean')]
            })

        # 3. IP维度聚合
        ip_stats = df.groupby('ip').agg({
            'operateUserId': 'nunique',  # 独立用户数
            'requestUrl': 'count',  # 访问次数
        }).reset_index()

        for _, row in ip_stats.iterrows():
            results.append({
                'stat_date': process_date,
                'dimension': 'ip',
                'dimension_value': row['ip'],
                'metric_name': 'visit_count',
                'metric_value': row[('requestUrl', 'count')]
            })
            results.append({
                'stat_date': process_date,
                'dimension': 'ip',
                'dimension_value': row['ip'],
                'metric_name': 'unique_users',
                'metric_value': row[('operateUserId', 'nunique')]
            })

        # 将结果保存到数据库
        with transaction.atomic():
            from src.apps.analytics.models import DailyStatistics

            # 先删除当天的数据（如果存在）
            DailyStatistics.objects.filter(stat_date=process_date).delete()

            # 批量创建新记录
            batch_size = 1000
            for i in range(0, len(results), batch_size):
                batch = results[i:i+batch_size]
                DailyStatistics.objects.bulk_create([
                    DailyStatistics(
                        stat_date=item['stat_date'],
                        dimension=item['dimension'],
                        dimension_value=item['dimension_value'],
                        metric_name=item['metric_name'],
                        metric_value=item['metric_value']
                    ) for item in batch
                ])

        logger.info(f"每日数据聚合任务完成，处理日期: {process_date}，共处理 {len(results)} 条记录")
        return {"message": "每日数据聚合任务完成", "affected_rows": len(results), "process_date": process_date}

    except Exception as e:
        logger.error(f"每日数据聚合任务失败: {str(e)}")
        raise


def weekly_aggregation_task(params):
    """
    每周数据聚合任务
    从daily_statistics表中提取上一周的数据，按周汇总后存入weekly_statistics表

    参数:
        params (dict): 任务参数，可包含自定义的周范围等

    返回:
        dict: 包含任务执行结果的字典
    """
    try:
        # 获取处理的年周，默认为上一周
        today = timezone.now().date()
        last_week_day = today - timedelta(days=today.weekday() + 7)
        year_week = params.get('year_week', last_week_day.strftime('%Y-%W'))

        # 计算上周的开始和结束日期
        year, week = year_week.split('-')
        start_date = datetime.strptime(f'{year}-{week}-1', '%Y-%W-%w').date()
        end_date = start_date + timedelta(days=6)

        logger.info(f"开始执行每周数据聚合任务，处理周: {year_week}，日期范围: {start_date} 至 {end_date}")

        # 从daily_statistics表中获取数据
        from src.apps.analytics.models import DailyStatistics
        daily_data = DailyStatistics.objects.filter(
            stat_date__gte=start_date,
            stat_date__lte=end_date
        ).values('dimension', 'dimension_value', 'metric_name', 'metric_value')

        # 使用pandas进行数据聚合
        df = pd.DataFrame(list(daily_data))

        if df.empty:
            logger.warning(f"周 {year_week} 没有找到数据")
            return {"message": "没有找到数据", "affected_rows": 0}

        # 按维度、维度值和指标名称分组聚合
        grouped = df.groupby(['dimension', 'dimension_value', 'metric_name']).agg({
            'metric_value': 'mean'  # 使用平均值作为聚合方法
        }).reset_index()

        # 将结果保存到数据库
        with transaction.atomic():
            from src.apps.analytics.models import WeeklyStatistics

            # 先删除当前周的数据（如果存在）
            WeeklyStatistics.objects.filter(year_week=year_week).delete()

            # 批量创建新记录
            batch_size = 1000
            records = []

            for _, row in grouped.iterrows():
                records.append(WeeklyStatistics(
                    year_week=year_week,
                    start_date=start_date,
                    end_date=end_date,
                    dimension=row['dimension'],
                    dimension_value=row['dimension_value'],
                    metric_name=row['metric_name'],
                    metric_value=row['metric_value']
                ))

            for i in range(0, len(records), batch_size):
                batch = records[i:i+batch_size]
                WeeklyStatistics.objects.bulk_create(batch)

        logger.info(f"每周数据聚合任务完成，处理周: {year_week}，共处理 {len(records)} 条记录")
        return {"message": "每周数据聚合任务完成", "affected_rows": len(records), "year_week": year_week}

    except Exception as e:
        logger.error(f"每周数据聚合任务失败: {str(e)}")
        raise


def monthly_aggregation_task(params):
    """
    每月数据聚合任务
    从daily_statistics表中提取上一月的数据，按月汇总后存入monthly_statistics表

    参数:
        params (dict): 任务参数，可包含自定义的月份范围等

    返回:
        dict: 包含任务执行结果的字典
    """
    try:
        # 获取处理的年月，默认为上一个月
        today = timezone.now().date()
        first_day_of_month = today.replace(day=1)
        last_month = (first_day_of_month - timedelta(days=1)).replace(day=1)
        year_month = params.get('year_month', last_month.strftime('%Y-%m'))

        # 计算上月的开始和结束日期
        year, month = year_month.split('-')
        start_date = datetime(int(year), int(month), 1).date()
        if int(month) == 12:
            end_date = datetime(int(year) + 1, 1, 1).date() - timedelta(days=1)
        else:
            end_date = datetime(int(year), int(month) + 1, 1).date() - timedelta(days=1)

        logger.info(f"开始执行每月数据聚合任务，处理月份: {year_month}，日期范围: {start_date} 至 {end_date}")

        # 从daily_statistics表中获取数据
        from src.apps.analytics.models import DailyStatistics
        daily_data = DailyStatistics.objects.filter(
            stat_date__gte=start_date,
            stat_date__lte=end_date
        ).values('dimension', 'dimension_value', 'metric_name', 'metric_value')

        # 使用pandas进行数据聚合
        df = pd.DataFrame(list(daily_data))

        if df.empty:
            logger.warning(f"月份 {year_month} 没有找到数据")
            return {"message": "没有找到数据", "affected_rows": 0}

        # 按维度、维度值和指标名称分组聚合
        grouped = df.groupby(['dimension', 'dimension_value', 'metric_name']).agg({
            'metric_value': 'mean'  # 使用平均值作为聚合方法
        }).reset_index()

        # 将结果保存到数据库
        with transaction.atomic():
            from src.apps.analytics.models import MonthlyStatistics

            # 先删除当前月的数据（如果存在）
            MonthlyStatistics.objects.filter(year_month=year_month).delete()

            # 批量创建新记录
            batch_size = 1000
            records = []

            for _, row in grouped.iterrows():
                records.append(MonthlyStatistics(
                    year_month=year_month,
                    dimension=row['dimension'],
                    dimension_value=row['dimension_value'],
                    metric_name=row['metric_name'],
                    metric_value=row['metric_value']
                ))

            for i in range(0, len(records), batch_size):
                batch = records[i:i+batch_size]
                MonthlyStatistics.objects.bulk_create(batch)

        logger.info(f"每月数据聚合任务完成，处理月份: {year_month}，共处理 {len(records)} 条记录")
        return {"message": "每月数据聚合任务完成", "affected_rows": len(records), "year_month": year_month}

    except Exception as e:
        logger.error(f"每月数据聚合任务失败: {str(e)}")
        raise


def quarterly_aggregation_task(params):
    """
    每季度数据聚合任务
    从monthly_statistics表中提取上一季度的数据，按季度汇总

    参数:
        params (dict): 任务参数，可包含自定义的季度范围等

    返回:
        dict: 包含任务执行结果的字典
    """
    try:
        # 获取处理的年季度，默认为上一个季度
        today = timezone.now().date()
        current_quarter = (today.month - 1) // 3 + 1
        current_year = today.year

        # 计算上一季度
        if current_quarter == 1:
            last_quarter = 4
            last_quarter_year = current_year - 1
        else:
            last_quarter = current_quarter - 1
            last_quarter_year = current_year

        year_quarter = params.get('year_quarter', f"{last_quarter_year}-Q{last_quarter}")

        # 解析年季度
        year, quarter = year_quarter.split('-Q')
        year = int(year)
        quarter = int(quarter)

        # 计算季度的月份范围
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3

        # 构建月份列表
        months = [f"{year}-{m:02d}" for m in range(start_month, end_month + 1)]

        logger.info(f"开始执行每季度数据聚合任务，处理季度: {year_quarter}，月份: {', '.join(months)}")

        # 从monthly_statistics表中获取数据
        from src.apps.analytics.models import MonthlyStatistics
        monthly_data = MonthlyStatistics.objects.filter(
            year_month__in=months
        ).values('dimension', 'dimension_value', 'metric_name', 'metric_value')

        # 使用pandas进行数据聚合
        df = pd.DataFrame(list(monthly_data))

        if df.empty:
            logger.warning(f"季度 {year_quarter} 没有找到数据")
            return {"message": "没有找到数据", "affected_rows": 0}

        # 按维度、维度值和指标名称分组聚合
        grouped = df.groupby(['dimension', 'dimension_value', 'metric_name']).agg({
            'metric_value': 'mean'  # 使用平均值作为聚合方法
        }).reset_index()

        # 将结果保存到数据库
        with transaction.atomic():
            from src.apps.analytics.models import QuarterlyStatistics

            # 先删除当前季度的数据（如果存在）
            QuarterlyStatistics.objects.filter(year_quarter=year_quarter).delete()

            # 批量创建新记录
            batch_size = 1000
            records = []

            for _, row in grouped.iterrows():
                records.append(QuarterlyStatistics(
                    year_quarter=year_quarter,
                    dimension=row['dimension'],
                    dimension_value=row['dimension_value'],
                    metric_name=row['metric_name'],
                    metric_value=row['metric_value']
                ))

            for i in range(0, len(records), batch_size):
                batch = records[i:i+batch_size]
                QuarterlyStatistics.objects.bulk_create(batch)

        logger.info(f"每季度数据聚合任务完成，处理季度: {year_quarter}，共处理 {len(records)} 条记录")
        return {"message": "每季度数据聚合任务完成", "affected_rows": len(records), "year_quarter": year_quarter}

    except Exception as e:
        logger.error(f"每季度数据聚合任务失败: {str(e)}")
        raise


def user_behavior_analysis_task(params=None):
    """
    用户行为分析数据生成任务
    每天生成前一天的用户行为分析数据，并写入tgt_api_user_operate_analysis_day表
    如果发现数据库中有缺失的日期，会自动补充生成这些日期的数据

    参数:
        params (dict): 任务参数，可包含自定义的日期范围等
            force_date: 强制处理指定日期的数据，格式为YYYY-MM-DD
            system_type: 系统类型，可选值为api(默认)、oss、boss
            all_systems: 是否处理所有系统的数据，如果为True，则忽略system_type参数，默认为False

    返回:
        dict: 包含任务执行结果的字典
    """
    try:
        params = params or {}
        logger.info("开始执行用户行为分析数据生成任务")

        # 获取当前日期和前一天日期
        now = timezone.now().date()
        yesterday = now - timedelta(days=1)

        # 获取系统类型参数
        system_type = params.get('system_type', 'api')
        all_systems = params.get('all_systems', False)

        if all_systems:
            logger.info("将处理所有系统类型的数据")
        else:
            logger.info(f"处理系统类型: {system_type}")

        # 检查是否指定了强制处理的日期
        force_date = params.get('force_date', None)
        if force_date:
            try:
                if isinstance(force_date, str):
                    force_date = datetime.strptime(force_date, '%Y-%m-%d').date()
                process_date = force_date
                logger.info(f"强制处理指定日期: {process_date}")
            except Exception as e:
                logger.error(f"解析强制处理日期失败: {str(e)}")
                process_date = yesterday
        else:
            process_date = yesterday
            logger.info(f"将处理前一天的数据: {process_date}")

        total_insert_count = 0
        system_results = {}

        if all_systems:
            # 处理所有系统的数据
            system_types = ['api', 'boss', 'oss']  # 先处理API和BOSS系统，再处理OSS系统（因为OSS数据量大）

            for sys_type in system_types:
                logger.info(f"开始处理 {sys_type} 系统的数据")
                # 获取该日期的聚合数据
                aggregated_data = get_aggregated_data(process_date, sys_type)

                if aggregated_data:
                    # 将数据插入数据库
                    insert_count = insert_user_behavior_data(process_date, aggregated_data, sys_type)
                    total_insert_count += insert_count
                    logger.info(f"{sys_type} 系统数据处理完成，插入了 {insert_count} 条记录")
                    system_results[sys_type] = {
                        "affected_rows": insert_count,
                        "status": "success"
                    }
                else:
                    logger.warning(f"{sys_type} 系统没有找到数据或聚合失败")
                    system_results[sys_type] = {
                        "affected_rows": 0,
                        "status": "no_data"
                    }

            logger.info(f"所有系统数据处理完成，共插入 {total_insert_count} 条记录")

            return {
                "message": "用户行为分析数据生成任务完成",
                "affected_rows": total_insert_count,
                "process_date": process_date.strftime('%Y-%m-%d'),
                "system_results": system_results
            }
        else:
            # 只处理指定系统的数据
            # 获取该日期的聚合数据
            aggregated_data = get_aggregated_data(process_date, system_type)

            if aggregated_data:
                # 将数据插入数据库
                insert_count = insert_user_behavior_data(process_date, aggregated_data, system_type)
                logger.info(f"共插入 {insert_count} 条记录")

                return {
                    "message": "用户行为分析数据生成任务完成",
                    "affected_rows": insert_count,
                    "process_date": process_date.strftime('%Y-%m-%d'),
                    "system_type": system_type
                }
            else:
                logger.warning(f"日期 {process_date} 的 {system_type} 系统没有找到数据或聚合失败")
                return {
                    "message": "没有找到数据或聚合失败",
                    "affected_rows": 0,
                    "process_date": process_date.strftime('%Y-%m-%d'),
                    "system_type": system_type
                }

    except Exception as e:
        logger.error(f"用户行为分析数据生成任务失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise

def get_aggregated_data(date, system_type='api'):
    """使用Elasticsearch聚合功能获取聚合数据"""
    logger.info(f"使用Elasticsearch聚合功能获取 {date} 系统类型 {system_type} 的聚合数据...")

    # 设置查询时间范围
    start_time = datetime.combine(date, datetime.min.time())
    end_time = datetime.combine(date, datetime.max.time())

    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

    logger.info(f"查询时间范围: {start_time_str} 到 {end_time_str}")

    try:
        # 获取ES索引和字段映射
        index_name = ElasticsearchIndices.get_operation_log_wildcard(system_type)
        field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

        # 构建Elasticsearch查询
        s = Search(using=es_client, index=index_name) \
            .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lt': end_time_str}})

        # 获取总数
        total_count = s.count()
        logger.info(f"找到 {total_count} 条记录")

        if total_count == 0:
            logger.warning("没有找到数据，跳过处理")
            return []

        # 使用Elasticsearch的Terms聚合功能
        # 1. 按用户ID和请求路径分组
        # 2. 计算每组的平均响应时间

        # 对于boss系统，使用operateUserId字段进行聚合
        if system_type == 'boss':
            user_id_field = 'operateUserId'
            logger.info(f"使用 {user_id_field} 字段进行聚合")
        else:
            user_id_field = field_mapping['user_id']
            logger.info(f"使用 {user_id_field} 字段进行聚合")

        # 使用足够大的size值确保获取所有数据
        # 对于Elasticsearch，默认只返回前10个桶，需要显式指定更大的值
        s.aggs.bucket('by_user', 'terms', field=user_id_field, size=10000) \
              .bucket('by_path', 'terms', field=field_mapping['request_path'], size=10000) \
              .metric('avg_cost', 'avg', field=field_mapping['cost'])

        # 执行查询
        response = s.execute()

        # 处理聚合结果
        aggregated_data = []

        if hasattr(response, 'aggregations') and hasattr(response.aggregations, 'by_user'):
            # 遍历用户分组
            for user_bucket in response.aggregations.by_user.buckets:
                user_id = user_bucket.key

                # 遍历每个用户的请求路径分组
                for path_bucket in user_bucket.by_path.buckets:
                    request_path = path_bucket.key
                    doc_count = path_bucket.doc_count
                    avg_cost = path_bucket.avg_cost.value

                    # 获取该组的一个样本记录，用于提取operateType和className
                    sample_query = Search(using=es_client, index=index_name) \
                        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lt': end_time_str}})

                    # 对于BOSS系统，使用operateUserId字段进行过滤
                    if system_type == 'boss':
                        sample_query = sample_query.filter('term', operateUserId=user_id)
                    else:
                        sample_query = sample_query.filter('term', **{field_mapping['user_id']: user_id})

                    sample_query = sample_query.filter('term', **{field_mapping['request_path']: request_path}) \
                        .sort(f"-{field_mapping['create_time']}")[0:1]

                    sample_response = sample_query.execute()

                    # 提取operateType、className和methodName
                    operate_type = None
                    class_name = None
                    method_name = None
                    request_name_analyzed = None
                    operate_user_name_analyzed = None

                    if len(sample_response.hits) > 0:
                        hit = sample_response.hits[0]
                        operate_type = getattr(hit, field_mapping['operate_type'], None)
                        class_name = getattr(hit, field_mapping['class_name'], None)
                        method_name = getattr(hit, field_mapping['method_name'], None)

                        # 对于BOSS系统，获取requestNameAnalyzed和operateUserNameAnalyzed字段的值
                        if system_type == 'boss':
                            # 直接从hit中获取原始字段值
                            request_name_analyzed = getattr(hit, 'requestNameAnalyzed', None)
                            operate_user_name_analyzed = getattr(hit, 'operateUserNameAnalyzed', None)
                            logger.info(f"BOSS系统样本记录: requestNameAnalyzed={request_name_analyzed}, operateUserNameAnalyzed={operate_user_name_analyzed}")

                    # 添加到聚合数据列表
                    data_item = {
                        'user_id': user_id,
                        'request_path': request_path,
                        'doc_count': doc_count,
                        'avg_cost': avg_cost,
                        'operate_type': operate_type,
                        'class_name': class_name,
                        'method_name': method_name
                    }

                    # 对于BOSS系统，添加requestNameAnalyzed和operateUserNameAnalyzed字段
                    if system_type == 'boss':
                        data_item['request_name_analyzed'] = request_name_analyzed
                        data_item['operate_user_name_analyzed'] = operate_user_name_analyzed

                    aggregated_data.append(data_item)

                    # 每处理100个组输出一次进度
                    if len(aggregated_data) % 100 == 0:
                        logger.info(f"已处理 {len(aggregated_data)} 个聚合组")

            logger.info(f"聚合完成，共有 {len(aggregated_data)} 条聚合记录")
            return aggregated_data
        else:
            logger.warning("没有找到聚合结果")
            return []
    except Exception as e:
        logger.error(f"聚合数据失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []


def insert_user_behavior_data(date, aggregated_data, system_type='api'):
    """将用户行为分析数据插入数据库"""
    logger.info(f"将 {date} 系统类型 {system_type} 的用户行为分析数据插入数据库...")

    try:
        # 先删除当天的数据（如果存在）
        deleted = UserOperateAnalysisDay.objects.filter(day=date, system_type=system_type).delete()
        logger.info(f"删除了 {deleted[0]} 条旧数据")

        # 准备要插入的数据
        records = []
        for item in aggregated_data:
            try:
                # 将浮点数转换为整数
                avg_time = int(round(item['avg_cost']))
                request_count = int(item['doc_count'])

                # 直接使用原始的字符串值
                request_type = str(item['operate_type']) if item['operate_type'] is not None else ''

                # 对于BOSS系统，使用requestNameAnalyzed和operateUserNameAnalyzed字段
                if system_type == 'boss' and 'request_name_analyzed' in item and 'operate_user_name_analyzed' in item:
                    request_url_name = str(item['request_name_analyzed']) if item['request_name_analyzed'] is not None else ''
                    user_id = str(item['operate_user_name_analyzed']) if item['operate_user_name_analyzed'] is not None else ''
                    logger.debug(f"BOSS系统数据插入: request_url_name={request_url_name}, user_id={user_id}")
                else:
                    # 使用method_name作为request_url_name
                    request_url_name = str(item['method_name']) if item['method_name'] is not None else ''
                    user_id = str(item['user_id']) if item['user_id'] is not None else ''

                record = UserOperateAnalysisDay(
                    request_url=item['request_path'],
                    request_user_id=user_id,
                    request_url_name=request_url_name,
                    request_type=request_type,
                    avg_time=avg_time,
                    request_count=request_count,
                    day=date,
                    created_time=timezone.now(),
                    system_type=system_type
                )
                records.append(record)
            except Exception as e:
                logger.error(f"处理数据项失败，跳过此项: {str(e)}")
                continue

        # 批量创建新记录
        if records:
            # 使用事务保证原子性
            with transaction.atomic():
                # 分批插入，避免一次插入过多数据
                batch_size = 100
                created_count = 0
                for i in range(0, len(records), batch_size):
                    batch = records[i:i+batch_size]
                    UserOperateAnalysisDay.objects.bulk_create(batch)
                    created_count += len(batch)
                    # 只在每1000条记录时输出进度
                    if created_count % 1000 == 0 or created_count == len(records):
                        logger.info(f"批量创建进度: {created_count}/{len(records)} 条记录")

            logger.info(f"成功插入 {created_count} 条记录")
            return created_count
        else:
            logger.warning("没有有效的记录需要创建")
            return 0
    except Exception as e:
        logger.error(f"插入数据失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 0




        latest_date = None
        # 使用Django ORM查询最新日期，避免直接使用原始 SQL
        try:
            from django.db.models import Max
            latest_date_obj = UserOperateAnalysisDay.objects.aggregate(Max('day'))
            latest_date = latest_date_obj['day__max']
            if latest_date:
                logger.info(f"数据库中最新数据日期: {latest_date}")
        except Exception as e:
            logger.error(f"查询最新数据日期失败: {str(e)}")
            # 如果查询失败，假设没有数据
            latest_date = None

        # 确定需要生成数据的日期范围
        dates_to_process = []

        # 检查是否指定了强制处理的日期
        force_date = params.get('force_date', None)
        process_date = params.get('process_date', None)

        # 优先使用process_date，如果没有再使用force_date
        if process_date:
            try:
                if isinstance(process_date, str):
                    process_date = datetime.strptime(process_date, '%Y-%m-%d').date()
                dates_to_process.append(process_date)
                logger.info(f"处理指定日期: {process_date}")
            except Exception as e:
                logger.error(f"解析处理日期失败: {str(e)}")
        elif force_date:
            try:
                if isinstance(force_date, str):
                    force_date = datetime.strptime(force_date, '%Y-%m-%d').date()
                dates_to_process.append(force_date)
                logger.info(f"强制处理指定日期: {force_date}")
            except Exception as e:
                logger.error(f"解析强制处理日期失败: {str(e)}")
        else:
            # 正常模式，处理缺失的日期
            if latest_date is None:
                # 如果没有数据，只生成前一天的数据
                dates_to_process.append(yesterday)
                logger.info(f"数据库中没有数据，将生成 {yesterday} 的数据")
            else:
                # 如果有数据，生成从最新数据日期后一天到前一天的数据
                start_date = latest_date + timedelta(days=1)
                if start_date <= yesterday:
                    current_date = start_date
                    while current_date <= yesterday:
                        dates_to_process.append(current_date)
                        current_date += timedelta(days=1)
                    logger.info(f"数据库中最新数据日期为 {latest_date}，将生成 {start_date} 到 {yesterday} 的数据")
                else:
                    logger.info(f"数据库中最新数据日期为 {latest_date}，已是最新数据，无需生成")

        # 如果没有需要处理的日期，直接返回
        if not dates_to_process:
            return {"message": "没有需要生成的数据", "affected_days": 0}

        # 处理每一天的数据
        total_records = 0
        processed_days = []

        for process_date in dates_to_process:
            try:
                # 设置查询时间范围
                start_time = datetime.combine(process_date, datetime.min.time())
                end_time = datetime.combine(process_date, datetime.max.time())

                start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
                end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

                logger.info(f"开始处理 {process_date} 的数据，时间范围: {start_time_str} 到 {end_time_str}")

                # 构建Elasticsearch查询
                s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD)\
                    .filter('range', createTime={'gte': start_time_str, 'lt': end_time_str})

                # 执行查询获取数据
                try:
                    # 不使用scan方法，而是直接执行查询并分页获取数据
                    # 这样可以避免认证问题
                    logger.info(f"开始查询数据...")

                    # 首先获取总数
                    total_count = s.count()
                    logger.info(f"找到 {total_count} 条记录")

                    # 如果数据量过大，分批获取
                    batch_size = 1000
                    data = []

                    for start in range(0, min(total_count, 10000), batch_size):  # 限制最多处理10000条数据
                        batch_query = s[start:start+batch_size]
                        batch_response = batch_query.execute()

                        logger.info(f"处理批次 {start//batch_size + 1}，获取 {len(batch_response.hits)} 条数据")

                        for hit in batch_response:
                            data.append({
                                'accountId': getattr(hit, 'accountId', None),
                                'requestPath': getattr(hit, 'requestPath', None),
                                'cost': getattr(hit, 'cost', 0),
                                'createTime': getattr(hit, 'createTime', None),
                                'result': getattr(hit, 'result', None),
                                'operateType': getattr(hit, 'operateType', None),
                                'className': getattr(hit, 'className', None),
                                'methodName': getattr(hit, 'methodName', None),
                            })

                    logger.info(f"查询成功，共获取 {len(data)} 条数据")

                    # 将数据转换为DataFrame
                    df = pd.DataFrame(data)

                    if df.empty:
                        logger.warning(f"日期 {process_date} 没有找到数据")
                        continue

                    logger.info(f"数据转换为DataFrame成功，开始聚合分析")
                except Exception as e:
                    logger.error(f"查询数据失败: {str(e)}")
                    raise



                # 按用户ID和请求路径分组聚合
                try:
                    # 确保数据类型正确
                    df['cost'] = pd.to_numeric(df['cost'], errors='coerce').fillna(0)

                    # 分组聚合
                    user_url_stats = df.groupby(['accountId', 'requestPath']).agg({
                        'cost': ['count', 'mean'],  # 请求次数和平均响应时间
                        'operateType': 'first',  # 业务类型
                    }).reset_index()

                    # 展平多索引列名
                    user_url_stats.columns = [' '.join(col).strip() if isinstance(col, tuple) else col for col in user_url_stats.columns]

                    # 重命名列
                    user_url_stats = user_url_stats.rename(columns={
                        'accountId': 'request_user_id',
                        'requestPath': 'request_url',
                        'cost count': 'request_count',
                        'cost mean': 'avg_time',
                        'operateType first': 'request_type'
                    })

                    logger.info(f"数据聚合完成，共有 {len(user_url_stats)} 条聚合记录")
                except Exception as e:
                    logger.error(f"数据聚合失败: {str(e)}")
                    raise

                # 直接使用methodName作为request_url_name
                # 打印原始数据的前几行，检查methodName字段
                logger.info("\n原始数据的前5行:")
                for i, (_, row) in enumerate(df.iterrows()):
                    if i >= 5:
                        break
                    logger.info(f"Row {i+1}:")
                    logger.info(f"  requestPath: {row.get('requestPath')}")
                    logger.info(f"  methodName: {row.get('methodName')}")
                    logger.info(f"  className: {row.get('className')}")

                # 创建一个字典，存储每个requestPath对应的methodName
                url_method_map = {}
                method_name_count = 0
                class_name_count = 0
                path_count = 0

                # 遍历原始数据，为每个requestPath找到对应的methodName
                for _, row in df.iterrows():
                    path = row.get('requestPath')
                    if not path:
                        continue

                    # 仅处理还没有对应方法名的路径
                    if path in url_method_map:
                        continue

                    # 优先使用methodName
                    method_name = row.get('methodName')
                    source = 'methodName'

                    # 如果methodName为空，尝试从路径中提取
                    if not method_name or pd.isna(method_name):
                        # 从路径中提取方法名
                        import re
                        match = re.search(r'/([^/]+)$', path)
                        if match:
                            method_name = match.group(1)
                            source = 'path'
                            path_count += 1
                        else:
                            # 如果无法从路径提取，使用className
                            method_name = row.get('className')
                            source = 'className'
                            class_name_count += 1
                    else:
                        method_name_count += 1

                    # 如果仍然为空，使用路径
                    if not method_name or pd.isna(method_name):
                        method_name = path
                        source = 'fallback'

                    # 记录路径和方法名的映射
                    url_method_map[path] = method_name

                    # 输出调试信息
                    logger.debug(f"Path: {path}, Method: {method_name}, Source: {source}")

                # 打印映射结果
                logger.info("\n路径到方法名的映射:")
                for i, (path, method) in enumerate(list(url_method_map.items())[:5]):
                    logger.info(f"{i+1}. {path} -> {method}")

                logger.info(f"methodName源: {method_name_count}, 路径提取: {path_count}, className源: {class_name_count}")

                # 将方法名映射到user_url_stats
                user_url_stats['request_url_name'] = user_url_stats['request_url'].map(url_method_map)

                # 如果映射后仍然有空值，使用request_url作为备用
                user_url_stats['request_url_name'] = user_url_stats['request_url_name'].fillna(user_url_stats['request_url'])

                # 将结果保存到数据库
                try:
                    with transaction.atomic():
                        # 先删除当天的数据（如果存在）
                        deleted_count = UserOperateAnalysisDay.objects.filter(day=process_date).delete()
                        logger.info(f"删除了 {process_date} 的 {deleted_count[0]} 条旧数据")

                        # 准备要插入的数据
                        records = []
                        for _, row in user_url_stats.iterrows():
                            try:
                                # 将浮点数转换为整数
                                avg_time = int(round(row['avg_time']))
                                request_count = int(row['request_count'])

                                # 处理可能的空值
                                request_type = int(row['request_type']) if pd.notna(row['request_type']) else 0
                                request_url_name = row['request_url_name'] if pd.notna(row['request_url_name']) else ''

                                records.append(UserOperateAnalysisDay(
                                    request_url=row['request_url'],
                                    request_user_id=row['request_user_id'],
                                    request_url_name=request_url_name,
                                    request_type=request_type,
                                    avg_time=avg_time,
                                    request_count=request_count,
                                    day=process_date,
                                    created_time=timezone.now()
                                ))
                            except Exception as e:
                                logger.warning(f"处理行数据失败，跳过此行: {str(e)}")
                                continue

                        # 批量创建新记录
                        if records:
                            batch_size = 1000
                            created_count = 0
                            for i in range(0, len(records), batch_size):
                                batch = records[i:i+batch_size]
                                created = UserOperateAnalysisDay.objects.bulk_create(batch)
                                created_count += len(created)
                                # 只在每1000条记录时输出进度
                                if created_count % 1000 == 0 or created_count == len(records):
                                    logger.info(f"批量创建进度: {created_count}/{len(records)} 条记录")
                        else:
                            logger.warning(f"没有有效的记录需要创建")
                except Exception as e:
                    logger.error(f"保存数据到数据库失败: {str(e)}")
                    raise

                total_records += len(records)
                processed_days.append(process_date.strftime('%Y-%m-%d'))

                logger.info(f"处理 {process_date} 的数据完成，生成了 {len(records)} 条记录")

            except Exception as e:
                logger.error(f"处理 {process_date} 的数据失败: {str(e)}")
                # 继续处理下一天的数据
                continue

        logger.info(f"用户行为分析数据生成任务完成，共处理了 {len(processed_days)} 天的数据，生成了 {total_records} 条记录")
        return {
            "message": "用户行为分析数据生成任务完成",
            "affected_rows": total_records,
            "affected_days": len(processed_days),
            "total_records": total_records,
            "processed_days": processed_days,
            "process_date": processed_days[0] if processed_days else None
        }

    except Exception as e:
        logger.error(f"用户行为分析数据生成任务失败: {str(e)}")
        raise