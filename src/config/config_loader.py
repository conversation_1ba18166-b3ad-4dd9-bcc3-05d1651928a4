# 配置文件加载器

import os
import yaml
from pathlib import Path

class ConfigLoader:
    """配置文件加载器"""
    
    def __init__(self, config_file=None):
        """
        初始化配置加载器
        
        Args:
            config_file: 配置文件路径，默认为项目根目录下的config.yml
        """
        if config_file is None:
            # 获取项目根目录
            base_dir = Path(__file__).resolve().parent.parent.parent
            config_file = base_dir / 'config.yml'
        
        self.config_file = config_file
        self._config = None
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            print(f"配置文件已加载: {self.config_file}")
        except FileNotFoundError:
            print(f"警告: 配置文件未找到 {self.config_file}，使用默认配置")
            self._config = self._get_default_config()
        except yaml.YAMLError as e:
            print(f"错误: 配置文件格式错误 {e}，使用默认配置")
            self._config = self._get_default_config()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'app': {
                'debug': True,
                'secret_key': 'django-insecure-default-key',
                'allowed_hosts': ['*'],
                'timezone': 'Asia/Shanghai',
                'language_code': 'zh-hans'
            },
            'database': {
                'engine': 'django.db.backends.mysql',
                'name': 'user_behavior_db',
                'user': 'root',
                'password': 'tgt51848',
                'host': '*************',
                'port': 3306
            },
            'elasticsearch': {
                'hosts': ['es-cn-x0r34fcdz0008x7a4.public.elasticsearch.aliyuncs.com:9200'],
                'username': 'elastic',
                'password': 'Tgt51848',
                'timeout': 180
            },
            'redis': {
                'host': '*************',
                'port': 6379,
                'db': 1
            }
        }
    
    def get(self, key_path, default=None):
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，支持点号分隔，如 'database.host'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_database_config(self):
        """获取数据库配置"""
        db_config = self.get('database', {})
        return {
            'ENGINE': db_config.get('engine', 'django.db.backends.mysql'),
            'NAME': db_config.get('name', 'user_behavior_db'),
            'USER': db_config.get('user', 'root'),
            'PASSWORD': db_config.get('password', 'tgt51848'),
            'HOST': db_config.get('host', '*************'),
            'PORT': db_config.get('port', 3306),
            'OPTIONS': db_config.get('options', {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'",
                'isolation_level': 'read committed',
            }),
            'TEST': db_config.get('test', {
                'CHARSET': 'utf8mb4',
                'COLLATION': 'utf8mb4_unicode_ci',
            }),
        }
    
    def get_elasticsearch_config(self):
        """获取Elasticsearch配置"""
        es_config = self.get('elasticsearch', {})
        return {
            'hosts': es_config.get('hosts', ['localhost:9200']),
            'username': es_config.get('username', ''),
            'password': es_config.get('password', ''),
            'timeout': es_config.get('timeout', 180),
            'retry_on_timeout': es_config.get('retry_on_timeout', True),
            'max_retries': es_config.get('max_retries', 10),
            'use_ssl': es_config.get('use_ssl', False),
            'verify_certs': es_config.get('verify_certs', False),
            'ca_certs': es_config.get('ca_certs'),
            'sniff_on_start': es_config.get('sniff_on_start', True),
            'sniff_on_connection_fail': es_config.get('sniff_on_connection_fail', True),
            'sniffer_timeout': es_config.get('sniffer_timeout', 60),
            'retry_on_status': es_config.get('retry_on_status', [500, 502, 503, 504, 429]),
            'maxsize': es_config.get('maxsize', 25),
            'block': es_config.get('block', True),
            'keep_alive': es_config.get('keep_alive', True),
            'keep_alive_timeout': es_config.get('keep_alive_timeout', 600),
        }
    
    def get_redis_config(self):
        """获取Redis配置"""
        redis_config = self.get('redis', {})
        host = redis_config.get('host', '127.0.0.1')
        port = redis_config.get('port', 6379)
        db = redis_config.get('db', 1)
        password = redis_config.get('password')
        
        location = f"redis://{host}:{port}/{db}"
        if password:
            location = f"redis://:{password}@{host}:{port}/{db}"
        
        return {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": location,
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
            }
        }
    
    def get_jwt_config(self, secret_key):
        """获取JWT配置"""
        from datetime import timedelta
        
        jwt_config = self.get('jwt', {})
        return {
            'ACCESS_TOKEN_LIFETIME': timedelta(hours=jwt_config.get('access_token_lifetime_hours', 1)),
            'REFRESH_TOKEN_LIFETIME': timedelta(days=jwt_config.get('refresh_token_lifetime_days', 7)),
            'ROTATE_REFRESH_TOKENS': jwt_config.get('rotate_refresh_tokens', False),
            'BLACKLIST_AFTER_ROTATION': jwt_config.get('blacklist_after_rotation', True),
            'ALGORITHM': jwt_config.get('algorithm', 'HS256'),
            'SIGNING_KEY': secret_key,
            'AUTH_HEADER_TYPES': tuple(jwt_config.get('auth_header_types', ['Bearer'])),
            'USER_ID_FIELD': 'id',
            'USER_ID_CLAIM': 'user_id',
            'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
            'TOKEN_TYPE_CLAIM': 'token_type',
        }
    
    def reload(self):
        """重新加载配置文件"""
        self._load_config()

# 创建全局配置实例
config = ConfigLoader()

# 便捷函数
def get_config(key_path, default=None):
    """获取配置值的便捷函数"""
    return config.get(key_path, default)

def reload_config():
    """重新加载配置的便捷函数"""
    config.reload()
