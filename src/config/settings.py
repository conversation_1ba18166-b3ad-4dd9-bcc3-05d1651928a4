# Django项目设置

import os
import logging
from pathlib import Path

# 导入配置加载器
from src.config.config_loader import config

# 导入日志配置
from src.config.logging_config import configure_logging

# 构建路径
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 安全设置
SECRET_KEY = 'django-insecure-your-secret-key-here'
DEBUG = True
ALLOWED_HOSTS = ['*']

# 应用定义
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 第三方应用
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    # 自定义应用
    'src.apps.users',
    'src.apps.features',
    'src.apps.analytics',
    'src.apps.reports',
    'src.apps.tasks',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 自定义中间件
    'src.middleware.performance_middleware.PerformanceMiddleware',  # 性能监控中间件（放在前面以准确记录耗时）
    'src.middleware.auth_middleware.AuthenticationMiddleware',  # 认证中间件
]

ROOT_URLCONF = 'src.config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'src.config.wsgi.application'

# 数据库配置 - 已优化为MySQL 5.7
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'user_behavior_db',
        'USER': 'root',
        'PASSWORD': 'tgt51848',
        'HOST': '*************',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            # 保留NO_AUTO_CREATE_USER选项，这在MySQL 5.7中是必需的，但在MySQL 8中已被移除
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'",
            'isolation_level': 'read committed',  # 适用于MySQL 5.7的隔离级别
        },
        'TEST': {
            'CHARSET': 'utf8mb4',
            'COLLATION': 'utf8mb4_unicode_ci',
        },
    }
}

# 添加MySQL版本兼容性设置
import pymysql

# 使用PyMySQL替代MySQLdb来支持MySQL 5.7
pymysql.version_info = (1, 4, 6, 'final', 0)  # 伪装成MySQLdb
pymysql.install_as_MySQLdb()

# 为Django 2.2设置DateTimeField类型
from django.db.backends.mysql.base import DatabaseWrapper
DatabaseWrapper.data_types['DateTimeField'] = 'datetime'  # 使用datetime而不是datetime(6)

# Elasticsearch配置
ELASTICSEARCH_CONFIG = {
    'hosts': ['es-cn-x0r34fcdz0008x7a4.public.elasticsearch.aliyuncs.com:9200'],  # ES服务器地址，可以是单个字符串或列表
    'username': 'elastic',  # ES用户名，如不需要认证可留空
    'password': 'Tgt51848',  # ES密码，如不需要认证可留空
    'timeout': 180,   # 连接超时时间（秒），增加到180秒
    'retry_on_timeout': True,  # 超时时是否重试
    'max_retries': 10,  # 最大重试次数，增加到10次
    'use_ssl': False,  # 是否使用SSL/TLS连接
    'verify_certs': False,  # 是否验证SSL证书
    'ca_certs': None,  # CA证书路径
    'sniff_on_start': True,  # 启动时是否对集群进行定位
    'sniff_on_connection_fail': True,  # 连接失败时是否对集群进行定位
    'sniffer_timeout': 60,  # 定位超时时间（秒）
    'retry_on_status': [500, 502, 503, 504, 429],  # 在这些状态码下重试
    # 连接池配置
    'maxsize': 25,  # 连接池最大连接数
    'block': True,  # 连接池满时阻塞
    # 连接保持配置
    'keep_alive': True,  # 保持连接
    'keep_alive_timeout': 600,  # 连接保持时间（秒）
}

# ES索引配置
ELASTICSEARCH_INDICES = {
    'logs': 'tgtweb_apioperatelog',  # 操作日志索引，成功和失败通过operateResult字段区分
}

# ES查询配置
ELASTICSEARCH_QUERY_CONFIG = {
    'size_limit': 10000,  # 单次查询最大返回数
    'scroll_timeout': '1m',  # 滚动查询超时时间
    'aggs_size_limit': 1000,  # 聚合桶大小限制
}

# Redis缓存配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://192.168.1.126:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

# 密码验证
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 国际化
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = False  # 禁用时区转换，直接使用本地时间（北京时间）

# 静态文件设置
STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# 默认主键字段类型 - 调整为Django 2.2兼容的设置
# 明确指定使用AutoField，与现有模型保持一致
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# REST Framework设置
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',  # 修改为需要认证才能访问
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# CORS设置
CORS_ALLOW_ALL_ORIGINS = True

# 设置默认的自动字段类型，避免Django 3.2+的警告
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型设置
# 暂时使用默认的Django User模型
# AUTH_USER_MODEL = 'users.User'

# JWT设置
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),  # 访问令牌有效期
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),  # 刷新令牌有效期
    'ROTATE_REFRESH_TOKENS': False,  # 是否在刷新令牌时生成新的刷新令牌
    'BLACKLIST_AFTER_ROTATION': True,  # 在刷新令牌时将旧令牌加入黑名单
    'ALGORITHM': 'HS256',  # 加密算法
    'SIGNING_KEY': SECRET_KEY,  # 签名密钥
    'AUTH_HEADER_TYPES': ('Bearer',),  # 认证头类型
    'USER_ID_FIELD': 'id',  # 用户ID字段
    'USER_ID_CLAIM': 'user_id',  # 用户ID声明
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),  # 认证令牌类
    'TOKEN_TYPE_CLAIM': 'token_type',  # 令牌类型声明
}

# Django日志配置 - 使用RotatingFileHandler避免多进程冲突
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{asctime} - {name} - {levelname} - {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'django_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django-web.log'),
            'maxBytes': 50*1024*1024,  # 50MB
            'backupCount': 10,
            'encoding': 'utf-8',
            'delay': True,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'django_error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django-error.log'),
            'maxBytes': 10*1024*1024,  # 10MB
            'backupCount': 10,
            'encoding': 'utf-8',
            'delay': True,
            'formatter': 'verbose',
        },

    },
    'loggers': {
        'django': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['django_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'src': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'root': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
        },
    },
}

# 配置日志系统
if os.environ.get('DISABLE_DJANGO_LOGGING') != 'True':
    configure_logging()