# Elasticsearch客户端配置
# 兼容Elasticsearch 7.10.0版本
# 简化版，不使用连接池，每次查询都创建新的连接
# 并在使用后关闭连接

import urllib3
import time
from elasticsearch import Elasticsearch, ConnectionTimeout, ConnectionError, TransportError
from elasticsearch_dsl import connections
from django.conf import settings
import logging
import functools

logger = logging.getLogger(__name__)

# 禁用urllib3警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 准备ES客户端参数
def get_es_params(use_retry=False):
    """获取ES客户端参数"""
    params = {
        'hosts': settings.ELASTICSEARCH_CONFIG.get('hosts', ['localhost:9200']),
        'timeout': settings.ELASTICSEARCH_CONFIG.get('timeout', 30),  # 减少超时时间，快速失败
        # HTTP连接配置
        'http_compress': True,  # 启用压缩
        'verify_certs': False,  # 跳过证书验证
        'send_get_body_as': 'POST',  # 避免GET请求体问题
    }

    # 添加认证信息
    if settings.ELASTICSEARCH_CONFIG.get('username'):
        params['http_auth'] = (
            settings.ELASTICSEARCH_CONFIG.get('username'),
            settings.ELASTICSEARCH_CONFIG.get('password')
        )

    # 根据需要添加重试参数
    if use_retry:
        params.update({
            'retry_on_timeout': True,
            'max_retries': 2,  # 减少重试次数，快速失败
            'retry_on_status': [500, 502, 503, 504, 429],
        })
    else:
        params.update({
            'retry_on_timeout': False,
            'max_retries': 0,
        })

    return params

# ES客户端代理类 - 每次创建新连接，用完就关闭
class ESClientProxy:
    """
    ES客户端代理类
    每次调用都创建新连接，用完立即关闭
    对外接口与原ES客户端完全一致
    """

    def __init__(self):
        # 创建一个临时客户端用于获取静态属性
        self._temp_client = Elasticsearch(**get_es_params(use_retry=True))

    def __getattr__(self, name):
        """
        代理所有ES客户端的方法和属性
        """
        # 先检查临时客户端是否有这个属性
        if hasattr(self._temp_client, name):
            attr = getattr(self._temp_client, name)

            # 如果是方法，返回代理方法
            if callable(attr):
                def proxy_method(*args, **kwargs):
                    # 创建新的ES客户端
                    client = Elasticsearch(**get_es_params(use_retry=True))
                    try:
                        # 获取并调用对应的方法
                        method = getattr(client, name)
                        result = method(*args, **kwargs)
                        logger.debug(f"ES操作成功: {name}")
                        return result
                    except Exception as e:
                        logger.error(f"ES操作失败 {name}: {str(e)}")
                        raise
                    finally:
                        # 确保连接被关闭
                        try:
                            client.close()
                            logger.debug("ES连接已关闭")
                        except Exception as e:
                            logger.warning(f"关闭ES连接时出错: {str(e)}")

                return proxy_method
            else:
                # 如果是属性，直接返回临时客户端的属性
                return attr

        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

# 替换原有的es_client
es_client = ESClientProxy()

# 注册默认连接 - 优化版
connections.configure(default=get_es_params(use_retry=True))

# 创建一个新的ES客户端实例
def create_new_client(use_retry=False):
    """
    创建一个新的ES客户端实例

    参数:
        use_retry (bool): 是否使用重试机制，默认为False

    返回:
        Elasticsearch: ES客户端实例
    """
    return Elasticsearch(**get_es_params(use_retry=use_retry))

# 关闭连接的装饰器
def with_new_client(func=None, use_retry=False):
    """
    使用新的ES客户端实例执行函数，并在函数执行完毕后关闭连接

    参数:
        func (callable): 要装饰的函数
        use_retry (bool): 是否使用重试机制，默认为False

    返回:
        callable: 装饰后的函数
    """
    def decorator(f):
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            # 创建新的客户端
            client = create_new_client(use_retry=use_retry)
            try:
                # 使用新客户端执行函数
                return f(*args, client=client, **kwargs)
            finally:
                # 无论函数是否成功执行，都关闭客户端
                try:
                    client.close()
                    logger.debug("关闭Elasticsearch连接")
                except Exception as e:
                    logger.warning(f"关闭Elasticsearch连接时出错: {str(e)}")
        return wrapper

    # 如果直接使用@with_new_client
    if func is not None:
        return decorator(func)

    # 如果使用@with_new_client(use_retry=True)
    return decorator

# 执行查询并关闭连接
def execute_query_and_close(query_func, *args, use_retry=False, **kwargs):
    """
    执行查询并关闭连接

    参数:
        query_func (callable): 查询函数
        *args: 传递给查询函数的位置参数
        use_retry (bool): 是否使用重试机制，默认为False
        **kwargs: 传递给查询函数的关键字参数

    返回:
        查询函数的返回值
    """
    # 创建新的客户端
    client = create_new_client(use_retry=use_retry)
    try:
        # 执行查询
        return query_func(client, *args, **kwargs)
    finally:
        # 关闭客户端
        try:
            client.close()
            logger.debug("关闭Elasticsearch连接")
        except Exception as e:
            logger.warning(f"关闭Elasticsearch连接时出错: {str(e)}")

# 打印初始化信息
logger.info(f"初始化Elasticsearch客户端，连接到: {settings.ELASTICSEARCH_CONFIG.get('hosts')}")