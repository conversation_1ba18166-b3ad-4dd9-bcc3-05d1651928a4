#!/usr/bin/env python
# 测试Docker构建脚本

import subprocess
import sys

def test_build():
    """测试Docker构建"""
    print("测试Docker构建...")
    
    try:
        # 构建镜像
        cmd = ["docker", "build", "-t", "userbehavior-app:test", "."]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Docker构建成功!")
            print("构建输出:")
            print(result.stdout)
        else:
            print("❌ Docker构建失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ Docker命令未找到，请确保Docker已安装")
        return False
    except Exception as e:
        print(f"❌ 构建过程中出错: {e}")
        return False
    
    return True

def test_run():
    """测试容器运行"""
    print("\n测试容器运行...")
    
    try:
        # 运行容器
        cmd = [
            "docker", "run", "--rm", "-d", 
            "--name", "userbehavior-test",
            "-p", "8001:8000",
            "userbehavior-app:test"
        ]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 容器启动成功!")
            container_id = result.stdout.strip()
            print(f"容器ID: {container_id}")
            
            # 等待几秒钟
            import time
            print("等待容器启动...")
            time.sleep(10)
            
            # 检查容器状态
            check_cmd = ["docker", "ps", "--filter", "name=userbehavior-test"]
            check_result = subprocess.run(check_cmd, capture_output=True, text=True)
            print("容器状态:")
            print(check_result.stdout)
            
            # 停止容器
            stop_cmd = ["docker", "stop", "userbehavior-test"]
            subprocess.run(stop_cmd, capture_output=True)
            print("测试容器已停止")
            
        else:
            print("❌ 容器启动失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 运行过程中出错: {e}")
        return False
    
    return True

def cleanup():
    """清理测试资源"""
    print("\n清理测试资源...")
    
    try:
        # 删除测试镜像
        cmd = ["docker", "rmi", "userbehavior-app:test"]
        subprocess.run(cmd, capture_output=True)
        print("✅ 测试镜像已删除")
    except:
        pass

def main():
    """主函数"""
    print("=" * 60)
    print("Docker构建测试")
    print("=" * 60)
    
    # 测试构建
    if not test_build():
        sys.exit(1)
    
    # 测试运行
    if not test_run():
        cleanup()
        sys.exit(1)
    
    # 清理
    cleanup()
    
    print("\n🎉 所有测试通过!")
    print("现在可以正常部署了:")
    print("  ./deploy.sh")
    print("  python docker_deploy.py build-run")

if __name__ == "__main__":
    main()
