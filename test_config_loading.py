#!/usr/bin/env python
# 测试配置文件加载功能

import os
import sys
import tempfile
from pathlib import Path

def create_test_config():
    """创建测试配置文件"""
    test_config_content = """
# 测试配置文件
app:
  debug: false
  secret_key: "test-secret-key"
  allowed_hosts:
    - "test.example.com"
  timezone: "Asia/Shanghai"

database:
  engine: "django.db.backends.mysql"
  name: "test_db"
  user: "test_user"
  password: "test_password"
  host: "test-host"
  port: 3307

elasticsearch:
  hosts:
    - "test-es-host:9200"
  username: "test_elastic"
  password: "test_es_password"
  timeout: 120

redis:
  host: "test-redis-host"
  port: 6380
  db: 2

scheduler:
  daily_task_hour: 2
  daily_task_minute: 30
  check_interval_seconds: 30
"""
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False, encoding='utf-8') as f:
        f.write(test_config_content)
        return f.name

def test_default_config():
    """测试默认配置加载"""
    print("=" * 60)
    print("测试1: 默认配置加载")
    print("=" * 60)
    
    try:
        from src.config.config_loader import ConfigLoader
        
        # 清除环境变量
        os.environ.pop('CONFIG_FILE_PATH', None)
        os.environ.pop('SPRING_CONFIG_LOCATION', None)
        
        config = ConfigLoader()
        
        print(f"✓ 配置文件路径: {config.config_file}")
        print(f"✓ 数据库主机: {config.get('database.host')}")
        print(f"✓ ES主机: {config.get('elasticsearch.hosts')}")
        print("✓ 默认配置加载成功")
        
    except Exception as e:
        print(f"❌ 默认配置加载失败: {e}")

def test_env_config():
    """测试环境变量配置加载"""
    print("\n" + "=" * 60)
    print("测试2: 环境变量配置加载")
    print("=" * 60)
    
    test_config_file = create_test_config()
    
    try:
        # 设置环境变量
        os.environ['CONFIG_FILE_PATH'] = test_config_file
        
        from src.config.config_loader import ConfigLoader
        
        config = ConfigLoader()
        
        print(f"✓ 配置文件路径: {config.config_file}")
        print(f"✓ 数据库主机: {config.get('database.host')}")
        print(f"✓ 数据库端口: {config.get('database.port')}")
        print(f"✓ ES主机: {config.get('elasticsearch.hosts')}")
        print(f"✓ Redis主机: {config.get('redis.host')}")
        print(f"✓ 调度器执行时间: {config.get('scheduler.daily_task_hour')}:{config.get('scheduler.daily_task_minute')}")
        
        # 验证配置值
        assert config.get('database.host') == 'test-host'
        assert config.get('database.port') == 3307
        assert config.get('redis.port') == 6380
        assert config.get('scheduler.daily_task_hour') == 2
        
        print("✓ 环境变量配置加载成功")
        
    except Exception as e:
        print(f"❌ 环境变量配置加载失败: {e}")
    finally:
        # 清理
        os.environ.pop('CONFIG_FILE_PATH', None)
        os.unlink(test_config_file)

def test_spring_config():
    """测试Spring Boot风格配置加载"""
    print("\n" + "=" * 60)
    print("测试3: Spring Boot风格配置加载")
    print("=" * 60)
    
    test_config_file = create_test_config()
    
    try:
        # 设置Spring Boot风格环境变量
        os.environ['SPRING_CONFIG_LOCATION'] = test_config_file
        
        from src.config.config_loader import ConfigLoader
        
        config = ConfigLoader()
        
        print(f"✓ 配置文件路径: {config.config_file}")
        print(f"✓ 应用密钥: {config.get('app.secret_key')}")
        print(f"✓ 允许的主机: {config.get('app.allowed_hosts')}")
        
        # 验证配置值
        assert config.get('app.secret_key') == 'test-secret-key'
        assert 'test.example.com' in config.get('app.allowed_hosts')
        
        print("✓ Spring Boot风格配置加载成功")
        
    except Exception as e:
        print(f"❌ Spring Boot风格配置加载失败: {e}")
    finally:
        # 清理
        os.environ.pop('SPRING_CONFIG_LOCATION', None)
        os.unlink(test_config_file)

def test_direct_path():
    """测试直接指定路径"""
    print("\n" + "=" * 60)
    print("测试4: 直接指定配置文件路径")
    print("=" * 60)
    
    test_config_file = create_test_config()
    
    try:
        from src.config.config_loader import ConfigLoader
        
        # 直接指定配置文件路径
        config = ConfigLoader(config_file=test_config_file)
        
        print(f"✓ 配置文件路径: {config.config_file}")
        print(f"✓ 数据库配置: {config.get_database_config()}")
        print(f"✓ ES配置: {config.get_elasticsearch_config()}")
        print(f"✓ Redis配置: {config.get_redis_config()}")
        
        print("✓ 直接路径配置加载成功")
        
    except Exception as e:
        print(f"❌ 直接路径配置加载失败: {e}")
    finally:
        # 清理
        os.unlink(test_config_file)

def test_priority():
    """测试配置优先级"""
    print("\n" + "=" * 60)
    print("测试5: 配置优先级测试")
    print("=" * 60)
    
    test_config_file1 = create_test_config()
    test_config_file2 = create_test_config()
    
    try:
        # 设置多个环境变量
        os.environ['CONFIG_FILE_PATH'] = test_config_file1
        os.environ['SPRING_CONFIG_LOCATION'] = test_config_file2
        
        from src.config.config_loader import ConfigLoader
        
        # 直接指定路径应该有最高优先级
        config = ConfigLoader(config_file=test_config_file2)
        
        print(f"✓ 使用直接指定的配置文件: {config.config_file}")
        assert str(config.config_file) == test_config_file2
        
        # 测试环境变量优先级
        config2 = ConfigLoader()
        print(f"✓ CONFIG_FILE_PATH优先于SPRING_CONFIG_LOCATION: {config2.config_file}")
        assert str(config2.config_file) == test_config_file1
        
        print("✓ 配置优先级测试成功")
        
    except Exception as e:
        print(f"❌ 配置优先级测试失败: {e}")
    finally:
        # 清理
        os.environ.pop('CONFIG_FILE_PATH', None)
        os.environ.pop('SPRING_CONFIG_LOCATION', None)
        os.unlink(test_config_file1)
        os.unlink(test_config_file2)

def main():
    """主测试函数"""
    print("配置文件加载功能测试")
    print("=" * 60)
    
    # 运行所有测试
    test_default_config()
    test_env_config()
    test_spring_config()
    test_direct_path()
    test_priority()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)
    
    print("\n使用示例:")
    print("1. 环境变量方式:")
    print("   export CONFIG_FILE_PATH=/data/conf/config.yml")
    print("   python run_server.py")
    
    print("\n2. Spring Boot风格:")
    print("   export SPRING_CONFIG_LOCATION=/data/conf/config.yml")
    print("   python run_server.py")
    
    print("\n3. 命令行参数:")
    print("   python run_server_with_config.py --config /data/conf/config.yml")
    
    print("\n4. Docker环境变量:")
    print("   docker run -e CONFIG_FILE_PATH=/app/conf/config.yml ...")

if __name__ == "__main__":
    main()
