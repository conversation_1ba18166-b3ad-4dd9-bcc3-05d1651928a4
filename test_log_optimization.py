#!/usr/bin/env python
# 测试日志优化效果

import os
import sys
import pymysql
pymysql.install_as_MySQLdb()
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.config.settings')
import django
django.setup()

from datetime import datetime, timedelta

print('=== 测试日志优化效果 ===')

# 直接调用内部函数来测试日志输出
print('调用慢函数查询逻辑...')

try:
    from src.apps.analytics.performance_stats import get_slow_function_stats

    start_time = '2025-07-28 00:00:00'
    end_time = '2025-07-28 23:59:59'
    min_avg_cost = 500
    system_type = 'boss'

    result = get_slow_function_stats(start_time, end_time, min_avg_cost, system_type)

    print(f'查询成功，返回 {len(result)} 个慢函数')
    if result:
        print(f'第一个慢函数: {result[0]["requestPath"]} (平均耗时: {result[0]["avgCost"]}ms)')

except Exception as e:
    print(f'查询失败: {str(e)}')

print('\n=== 日志优化验证完成 ===')
print('检查控制台输出，应该看到：')
print('✅ 简洁的查询开始信息')
print('✅ 聚合结果数量统计')
print('❌ 没有详细的聚合结果JSON')
print('❌ 没有详细的样本记录JSON')
print('❌ 没有详细的查询JSON')
